<template>
	<view class="page">
		<map id="map" class="map" :show-location="true" :latitude="latitude" :longitude="longitude"></map>
		<view class="card">
			<view class="title">已通知多位师傅为您报价</view>
			<view class="desc">师傅可能在服务，请耐心等待</view>
			<view class="time">
				距师傅报价截止还剩：
				<u-count-down :time="24 * 60 * 60 * 1000" format="HH:mm:ss"></u-count-down>
			</view>
		</view>
		<view class="footer">
			<view @click.stop="cancelorder()" class="btn">取消订单</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			latitude: '', // Latitude for map
			longitude: '', // Longitude for map
			id: '', // Order ID
			currentIndex: 0, // Default payType for getList
			page: 1, // Default page number
			orderList: [], // Store fetched orders
			status: 'loadmore', // Load status for pagination
			showCancel: false // Manage dialog state
		}
	},
	methods: {
		// Fetch order list
		getList(nval) {
			const payType = nval !== undefined ? nval : this.currentIndex;
			return this.$api.service.userOrder({
				payType,
				pageNum: this.page,
				pageSize: 10
			}).then(res => {
				const list = Array.isArray(res.list) ? res.list : [];
				const normalizedList = list.map(item => ({
					...item,
					payType: parseInt(item.payType)
				}));
				this.$set(this, 'orderList', normalizedList);
				this.status = list.length < 10 ? 'nomore' : 'loadmore';
				// Assign the first order's id
				if (normalizedList.length > 0) {
					this.id = normalizedList[0].id;
				}
			}).catch(err => {
				uni.showToast({
					icon: 'error',
					title: '获取订单失败'
				});
				console.error('API Error:', err);
			});
		},
		// Show cancel order confirmation
		cancelorder() {
			uni.showModal({
				title: '提示',
				content: '确定要取消订单吗？',
				confirmText: '确定',
				cancelText: '取消',
				success: (res) => {
					if (res.confirm) {
						this.confirmCancel();
					}
				},
				fail: (err) => {
					console.error('Modal Error:', err);
				}
			});
		},
		// Confirm order cancellation
		confirmCancel() {
			this.showCancel = false;
			this.$api.service.cancelOrder({
				id: this.id
			}).then(res => {
				if (res.code === "200") {
					uni.showToast({
						icon: 'success',
						title: '取消成功'
					});
					// Navigate back to order list after cancellation
					uni.navigateBack({
						delta:9
					})
				}
			}).catch(err => {
				uni.showToast({
					icon: 'error',
					title: '取消失败'
				});
				console.error('Cancel Error:', err);
			});
		}
	},
	onLoad(options) {
		// Fetch order list
		this.getList();
		// Get user location
		uni.getLocation({
			type: 'gcj02',
			isHighAccuracy: true,
			accuracy: 'best',
			success: (res) => {
				this.latitude = res.latitude;
				this.longitude = res.longitude;
			},
			fail: (err) => {
				uni.showToast({
					icon: 'error',
					title: '获取位置失败'
				});
				console.error('Get Location Error:', err);
			}
		});
	},
	onBackPress() {
		// Override back button to navigate to order_list
		uni.redirectTo({
			url: '/user/order_list'
		});
		return true; // Prevent default back behavior
	},
		onUnload() {
					// uni.navigateBack({
					// 	delta:9
					// })
					uni.reLaunch({
					        url: '/user/tiaozhuan'
					    });
				},
	
}
</script>

<style scoped lang="scss">
.page {
	width: 750rpx;
	height: 100vh;

	.map {
		width: 100%;
		height: 100%;
	}

	.card {
		width: 686rpx;
		height: 234rpx;
		background: #FFFFFF;
		border-radius: 16rpx;
		position: absolute;
		left: 32rpx;
		top: 40rpx;
		padding: 34rpx;

		.title {
			font-size: 40rpx;
			font-weight: 500;
			color: #333333;
		}

		.desc {
			margin-top: 20rpx;
			font-size: 24rpx;
			font-weight: 400;
			color: #ADADAD;
		}

		.time {
			display: flex;
			align-items: center;
			margin-top: 20rpx;
			font-size: 24rpx;
			font-weight: 400;
			color: #ADADAD;

			::v-deep .u-count-down__text {
				color: #E72427;
			}
		}
	}

	.footer {
		width: 750rpx;
		height: 192rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		position: fixed;
		bottom: 0;
		background-color: #FFFFFF;

		.btn {
			width: 686rpx;
			height: 88rpx;
			background: #2E80FE;
			border-radius: 44rpx;
			font-size: 28rpx;
			font-weight: 400;
			color: #FFFFFF;
			line-height: 88rpx;
			text-align: center;
		}
	}
}
</style>