{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/shifuGrade.vue?12a3", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/shifuGrade.vue?6f59", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/shifuGrade.vue?cac4", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/shifuGrade.vue?fa0d", "uni-app:///shifu/shifuGrade.vue", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/shifuGrade.vue?dfcb", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/shifuGrade.vue?8f67"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "labellist", "info", "currentGrade", "computed", "upgradeOptions", "item", "upgradePrice", "upgradeDisabled", "methods", "getCurrentPlatform", "refreshData", "Promise", "console", "handleAppWechatPay", "uni", "orderInfo", "appid", "noncestr", "package", "partnerid", "prepayid", "timestamp", "sign", "title", "icon", "handleMiniProgramPay", "timeStamp", "nonceStr", "signType", "paySign", "appId", "success", "fail", "calculateUpgradePrice", "totalPrice", "getmyGradeList", "res", "getmyG<PERSON>", "handleUpgrade", "content", "labelId", "price", "url", "getUpgradePathText", "pathParts", "formatDate", "onLoad", "onPullDownRefresh", "duration"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,mBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAuH;AACvH;AAC8D;AACL;AACa;;;AAGtE;AACsN;AACtN,gBAAgB,8NAAU;AAC1B,EAAE,gFAAM;AACR,EAAE,qFAAM;AACR,EAAE,8FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,yFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACxBA;AAAA;AAAA;AAAA;AAAy1B,CAAgB,y2BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC+C72B;EACAC;IACA;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACAC;MAAA;MACA;;MAEA;MACA;QAAA;MAAA;MAEA;QACA;QACA;QAEA,uCACAC;UACAC;UACAC;QAAA;MAEA;QAAA;MAAA;IACA;EACA;;EACAC;IACAC;MAKA;MAKA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAGAC,aACA,yBACA,oBACA;cAAA;gBACAC;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACA;IACAC;MAAA;QAAA;MACAD;MACAE;QACA;QACAC;MAAA,mEACA;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA,kEACA;QACAV;QACAE;UACAS;UACAC;QACA;QACA;QACA;MACA,+DACA;QACAZ;QACA;UACAE;YACAS;YACAC;UACA;QACA;UACAV;YACAS;YACAC;UACA;QACA;MACA,yBACA;IACA;IAEA;IACAC;MAAA;MACAb;MACA;QACAc;QAAA;QACAC;QACAT;QACAU;QACAC;MACA;MACAjB;MACAE;QACA;QACAY;QACAC;QACAT;QACAC;QACAS;QACAC;QACAC;QACAC;UACA;UACAnB;UACAE;YACAS;YACAC;UACA;UACA;UACA;QACA;QACAQ;UACA;UACApB;UACAA;UACA;YACAE;cACAS;cACAC;YACA;UACA;YACAV;cACAS;cACAC;YACA;UACA;UACAZ;UACAE;YACAS;YACAC;UACA;QACA;MACA;IACA;IAmBA;IACAS;MACA;;MAEA;MACA;QACA;MACA;;MAEA;MACA;QAAA;MAAA;MACA;QAAA;MAAA;MAEA;QACA;MACA;;MAEA;MACA;MACA;QACAC;MACA;MAEA;IACA;IAEAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAC;gBACAxB;gBACA;gBACAA;gBACA;gBACA;kBACA;oBAAA;kBAAA;kBACAA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAA;gBACAE;kBACAS;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACAa;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAD;gBACAxB;gBACA;gBACAA;gBACA;gBACA;kBACA;oBAAA;kBAAA;kBACAA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAA;gBACAE;kBACAS;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACAc;MAAA;MACA1B;MACAA;;MAEA;MACA;QACAE;UACAS;UACAC;QACA;QACA;MACA;MAEA;QACAV;UACAS;UACAC;QACA;QACA;MACA;;MAEA;MACA;QAAA;MAAA;MACA;MACA;MACAV;QACAS;QACAgB;QACAR;UACA;YACA;YACA;YACA;YACA;YACA;;YAEA;YACA;YACA;YACA;;YAGA;YACA;cACAS;cACAC;YACA;cACA;;cAEA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;;cAGA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;;cAKA;cACA;cACA;cACA;cACA;cACA;;cAEA;gBACA3B;kBACAS;kBACAgB;kBACAR;oBACA;sBACAjB;wBACA4B;sBACA;oBACA;kBACA;gBACA;cACA;gBACA9B;gBACA;gBACA;gBACAA;gBACAA;gBACAA;gBACAA;gBACAA;gBACAA;;gBAEA;gBACA;gBACAA;gBAEA;kBACAc;kBAAA;kBACAC;kBACAT;kBACAU;kBACAC;gBACA;gBACAjB;;gBAEA;gBACA;kBACA;kBACAA;kBACA;gBACA;kBACA;kBACAA;kBACA;gBACA;kBACA;kBACAA;kBACA;gBACA;cACA;YAEA;cACAA;cACAE;gBACAS;gBACAC;cACA;YACA;UAEA;QACA;MACA;IACA;IAEA;IACAmB;MACA;QAAA;MAAA;MACA;QAAA;MAAA;MAEA;QACA;MACA;MAEA;MACA;QACAC;MACA;MAEA;IACA;IACAC;MACA;MACA;MACA;IACA;EACA;EACAC;IACA;IACA;EACA;EAEA;EACAC;IAAA;IAAA;MAAA;QAAA;UAAA;YAAA;cAAA;cAAA;cAAA,OAEA;YAAA;cACAjC;gBACAS;gBACAC;gBACAwB;cACA;cAAA;cAAA;YAAA;cAAA;cAAA;cAEApC;cACAE;gBACAS;gBACAC;cACA;YAAA;cAAA;cAEAV;cAAA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EAEA;AACA;AAAA,2B;;;;;;;;;;;;;AC5fA;AAAA;AAAA;AAAA;AAA6sC,CAAgB,gsCAAG,EAAC,C;;;;;;;;;;;ACAjuC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "shifu/shifuGrade.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/uni-stat/dist/uni-stat.es.js';\nimport Vue from 'vue'\nimport Page from './shifu/shifuGrade.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./shifuGrade.vue?vue&type=template&id=2381d9ae&\"\nvar renderjs\nimport script from \"./shifuGrade.vue?vue&type=script&lang=js&\"\nexport * from \"./shifuGrade.vue?vue&type=script&lang=js&\"\nimport style0 from \"./shifuGrade.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"shifu/shifuGrade.vue\"\nexport default component.exports", "export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./shifuGrade.vue?vue&type=template&id=2381d9ae&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.__map(_vm.upgradeOptions, function (item, __i0__) {\n    var $orig = _vm.__get_orig(item)\n    var g0 = item.id !== _vm.info.labelId ? item.upgradePrice.toFixed(2) : null\n    return {\n      $orig: $orig,\n      g0: g0,\n    }\n  })\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./shifuGrade.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./shifuGrade.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"container\">\n    <view class=\"current-grade\">\n      <text class=\"current-label\">当前等级:</text>\n      <text class=\"current-value\">{{ info.labelName }}</text>\n      <!-- <text class=\"current-id\">(ID: {{info.labelId}})</text> -->\n    </view>\n\n    <view class=\"upgrade-title\">可升级选项:</view>\n\n    <view class=\"grade-list\">\n      <view class=\"grade-item\" v-for=\"item in upgradeOptions\" :key=\"item.id\"\n        :class=\"{ 'current-grade-item': item.id === info.labelId }\">\n        <view class=\"grade-header\">\n          <text class=\"grade-name\">{{ item.labelName }}</text>\n          <!-- <text class=\"grade-id\">ID: {{item.id}}</text> -->\n        </view>\n\n        <view class=\"grade-details\">\n          <view class=\"detail-item\">\n            <text class=\"detail-label\">保证金:</text>\n            <text class=\"detail-value\">¥{{ item.earnestMoney }}</text>\n          </view>\n          <!-- <view class=\"detail-item\">\n            <text class=\"detail-label\">延迟时间:</text>\n            <text class=\"detail-value\">{{item.delayTime}}天</text>\n          </view> -->\n          <view v-if=\"item.id !== info.labelId\" class=\"detail-item\">\n            <text class=\"detail-label\">升级需支付:</text>\n            <text class=\"detail-value upgrade-price\">¥{{ item.upgradePrice.toFixed(2) }}</text>\n          </view>\n          <view v-else class=\"detail-item\">\n            <text class=\"detail-label\">当前等级</text>\n            <text class=\"detail-value current-tag\">✓</text>\n          </view>\n        </view>\n\n        <button v-if=\"item.id !== info.labelId\" class=\"upgrade-btn\" @click=\"handleUpgrade(item)\"\n          :disabled=\"item.upgradeDisabled\">\n          {{ item.upgradeDisabled ? '不可升级' : '立即升级' }}\n        </button>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nexport default {\n  data() {\n    return {\n      labellist: [],\n      info: {},\n      currentGrade: null\n    }\n  },\n  computed: {\n    // 计算可升级的选项\n    upgradeOptions() {\n      if (!this.currentGrade || !this.labellist.length) return [];\n\n      // 按保证金从低到高排序，用于计算升级路径\n      const sortedGrades = [...this.labellist].sort((a, b) => a.earnestMoney - b.earnestMoney);\n\n      return this.labellist.map(item => {\n        // 计算升级价格：累加从当前等级到目标等级之间的所有等级保证金\n        const upgradePrice = this.calculateUpgradePrice(this.currentGrade, item, sortedGrades);\n\n        return {\n          ...item,\n          upgradePrice: upgradePrice > 0 ? upgradePrice : 0,\n          upgradeDisabled: upgradePrice <= 0 // 如果价格<=0则不可升级（降级或同级）\n        };\n      }).sort((a, b) => b.earnestMoney - a.earnestMoney); // 按保证金从高到低排序显示\n    }\n  },\n  methods: {\n    getCurrentPlatform() {\n      // #ifdef APP-PLUS\n      return 'app-plus';\n      // #endif\n      // #ifdef MP-WEIXIN\n      return 'mp-weixin';\n      // #endif\n      // #ifdef H5\n      return 'h5';\n      // #endif\n      return 'unknown';\n    },\n\n    // 刷新数据方法\n    async refreshData() {\n      try {\n        // 重新获取等级列表和当前等级信息\n        await Promise.all([\n          this.getmyGradeList(),\n          this.getmyGrade()\n        ]);\n        console.log('数据刷新成功');\n      } catch (error) {\n        console.error('数据刷新失败:', error);\n      }\n    },\n    // APP微信支付处理\n    handleAppWechatPay(obj) {\n      console.log(111)\n      uni.requestPayment({\n        \"provider\": \"wxpay\",\n        orderInfo: 'orderInfo',\n        orderInfo: {\n          appid: obj.appId,\n          noncestr: obj.nonceStr,\n          package: 'Sign=WXPay',\n          partnerid: obj.partnerId,\n          prepayid: obj.prepayId,\n          timestamp: String(obj.timestamp),\n          sign: obj.sign\n        },\n        success: (res) => {\n          console.log('APP微信支付成功', res);\n          uni.showToast({\n            title: '支付成功',\n            icon: 'success'\n          })\n          // 支付成功后刷新数据\n          this.refreshData();\n        },\n        fail: (err) => {\n          console.error('APP微信支付失败:', err);\n          if (err.errMsg && err.errMsg.includes('cancel')) {\n            uni.showToast({\n              title: '您已取消支付',\n              icon: 'none'\n            });\n          } else {\n            uni.showToast({\n              title: '支付失败，请稍后重试',\n              icon: 'none'\n            });\n          }\n        }\n      });\n    },\n\n    // 微信小程序支付处理（保持原有逻辑）\n    handleMiniProgramPay(obj) {\n      console.log(obj)\n      const paymentParams = {\n        timeStamp: String(obj.timestamp), // 一定要是 string\n        nonceStr: obj.nonceStr,\n        package: \"prepay_id=\" + obj.prepayId,\n        signType: 'MD5',\n        paySign: obj.sign\n      };\n      console.log(JSON.stringify(paymentParams));\n      uni.requestPayment({\n        \"provider\": 'wxpay',\n        timeStamp: String(obj.timestamp),\n        nonceStr: obj.nonceStr,\n        package: \"prepay_id=\" + obj.prepayId,\n        partnerid: obj.partnerId,\n        signType: \"MD5\",\n        paySign: obj.sign,\n        appId: obj.appId,\n        success: (res1) => {\n          // 支付成功回调\n          console.log('支付成功', res1);\n          uni.showToast({\n            title: '支付成功',\n            icon: 'success'\n          })\n          // 支付成功后刷新数据\n          this.refreshData();\n        },\n        fail: (err) => {\n          // 支付失败回调\n          console.error('requestPayment fail object:', err);\n          console.error('requestPayment fail JSON:', JSON.stringify(err));\n          if (err.errMsg.includes('fail cancel')) {\n            uni.showToast({\n              title: '您已取消支付',\n              icon: 'none'\n            });\n          } else {\n            uni.showToast({\n              title: '支付失败，请稍后重试',\n              icon: 'none'\n            });\n          }\n          console.error('支付失败', err);\n          uni.showToast({\n            title: '支付失败请检查网络',\n            icon: 'error'\n          })\n        },\n      })\n    },\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n    // 计算升级价格：累加从当前等级到目标等级之间的所有等级保证金\n    calculateUpgradePrice(currentGrade, targetGrade, sortedGrades) {\n      if (!currentGrade || !targetGrade) return 0;\n\n      // 如果目标等级保证金小于等于当前等级，不能升级\n      if (targetGrade.earnestMoney <= currentGrade.earnestMoney) {\n        return 0;\n      }\n\n      // 找到当前等级和目标等级在排序数组中的位置\n      const currentIndex = sortedGrades.findIndex(grade => grade.id === currentGrade.id);\n      const targetIndex = sortedGrades.findIndex(grade => grade.id === targetGrade.id);\n\n      if (currentIndex === -1 || targetIndex === -1 || targetIndex <= currentIndex) {\n        return 0;\n      }\n\n      // 累加从当前等级的下一级到目标等级的所有保证金\n      let totalPrice = 0;\n      for (let i = currentIndex + 1; i <= targetIndex; i++) {\n        totalPrice += sortedGrades[i].earnestMoney;\n      }\n\n      return totalPrice;\n    },\n\n    async getmyGradeList() {\n      try {\n        const res = await this.$api.shifu.getGradeList();\n        console.log('等级列表:', res)\n        this.labellist = res.data\n        console.log('labellist:', this.labellist)\n        // 如果已有当前等级信息，则查找对应的完整信息\n        if (this.info.labelId && this.labellist.length) {\n          this.currentGrade = this.labellist.find(item => item.id === this.info.labelId);\n          console.log('当前等级完整信息:', this.currentGrade)\n        }\n      } catch (err) {\n        console.error('获取等级列表失败:', err)\n        uni.showToast({\n          title: '获取等级列表失败',\n          icon: 'error'\n        });\n      }\n    },\n    async getmyGrade() {\n      try {\n        const res = await this.$api.shifu.getGrade();\n        console.log('当前等级:', res)\n        this.info = res.data\n        console.log('info:', this.info)\n        // 如果已有等级列表，则查找对应的完整信息\n        if (this.labellist.length) {\n          this.currentGrade = this.labellist.find(item => item.id === this.info.labelId);\n          console.log('当前等级完整信息:', this.currentGrade)\n        }\n      } catch (err) {\n        console.error('获取当前等级失败:', err)\n        uni.showToast({\n          title: '获取当前等级失败',\n          icon: 'error'\n        });\n      }\n    },\n    handleUpgrade(item) {\n      console.log('升级选择:', item)\n      console.log('当前等级:', this.currentGrade)\n\n      // 验证升级条件\n      if (item.upgradeDisabled) {\n        uni.showToast({\n          title: '该等级不可升级',\n          icon: 'error'\n        });\n        return;\n      }\n\n      if (item.upgradePrice <= 0) {\n        uni.showToast({\n          title: '不能降级或升级同级',\n          icon: 'error'\n        });\n        return;\n      }\n\n      // 计算升级路径说明\n      const sortedGrades = [...this.labellist].sort((a, b) => a.earnestMoney - b.earnestMoney);\n      const upgradePathText = this.getUpgradePathText(this.currentGrade, item, sortedGrades);\n      // let  that =this\n      uni.showModal({\n        title: '确认升级',\n        content: `确定要升级到${item.labelName}吗？\\n${upgradePathText}\\n总计需要支付¥${item.upgradePrice.toFixed(2)}`,\n        success: (res) => {\n          if (res.confirm) {\n            // 这里可以取消注释来实现真正的升级API调用\n            // uni.showToast({\n            //   title: '升级成功',\n            //   icon: 'success'\n            // });\n\n            // 模拟升级成功后刷新数据\n            // setTimeout(() => {\n            //   this.getmyGrade();\n            // }, 1000);\n\n\n            // 真正的升级API调用\n            this.$api.shifu.upgradeGrade({\n              labelId: item.id,\n              price: item.upgradePrice.toFixed(2)\n            }).then(res => {\n              //       if(res.code==='200'){\n\n              //  console.log(res)\n              //  let obj = res.data\n              //  let packageStr = \"prepay_id=\" + obj.prepayId;\n              //  console.log(String(packageStr))\n              //  console.log(obj.nonceStr)\n              //  console.log(packageStr)\n              //  console.log(obj.nonceStr)\n              //  console.log(String(obj.timestamp))\n              //  console.log(obj.sign)\n              //  const paymentParams = {\n              //  \ttimeStamp: String(obj.timestamp), // 一定要是 string \n              //  \tnonceStr: obj.nonceStr,\n              //  \tpackage: \"prepay_id=\" + obj.prepayId,\n              //  \tsignType: 'MD5',\n              //  \tpaySign: obj.sign\n              //  };\n              //  console.log(JSON.stringify(paymentParams));\n              //  uni.requestPayment({\n              //  \t\"provider\": 'wxpay',\n              //  \ttimeStamp: String(obj.timestamp),\n              //  \tnonceStr: obj.nonceStr,\n              //  \tpackage: \"prepay_id=\" + obj.prepayId,\n              //  \tpartnerid: obj.partnerId,\n              //  \tsignType: \"MD5\",\n              //  \tpaySign: obj.sign,\n              //  \tappId: obj.appId,\n              //  \tsuccess: (res1) => {\n              //  \t\t// 支付成功回调\n              //  \t\tconsole.log('支付成功', res1);\n              //  \t\tuni.showToast({\n              //  \t\t\ttitle:res1.msg,\n              //  \t\t\ticon: 'success'\n              //  \t\t})\n\n\n              //  \t\tthis.getmyGrade(); // 刷新当前等级\n              //  \t},\n              //  \tfail: (err) => {\n              //  \t\t// 支付失败回调\n              //  \t\tconsole.error('requestPayment fail object:', err);\n              //  \t\tconsole.error('requestPayment fail JSON:', JSON.stringify(err));\n              //  \t\tif (err.errMsg.includes('fail cancel')) {\n              //  \t\t\twx.showToast({\n              //  \t\t\t\ttitle: '您已取消支付',\n              //  \t\t\t\ticon: 'none'\n              //  \t\t\t});\n              //  \t\t} else {\n              //  \t\t\twx.showToast({\n              //  \t\t\t\ttitle: '支付失败，请稍后重试',\n              //  \t\t\t\ticon: 'none'\n              //  \t\t\t});\n              //  \t\t}\n              //  \t\tconsole.error('支付失败', err);\n              //  \t\tuni.showToast({\n              //  \t\t\ttitle: '支付失败请检查网络',\n              //  \t\t\ticon: 'error'\n              //  \t\t})\n              //  \t},\n              //  })\n\n\n\n\n              // }else{\n              //  uni.showToast({\n              //  \ttitle: res.msg,\n              //  \ticon: 'none'\n              //  })\n              // }\n\n              if (res.data === -1) {\n                uni.showModal({\n                  title: '提示',\n                  content: '确定要跳转到保证金页面吗？',\n                  success: (confirmRes) => {\n                    if (confirmRes.confirm) {\n                      uni.navigateTo({\n                        url: '/shifu/Margin'\n                      });\n                    }\n                  }\n                });\n              } else if (res.code === '200') {\n                console.log(res)\n                let obj = res.data\n                let packageStr = \"prepay_id=\" + obj.prepayId;\n                console.log(String(packageStr))\n                console.log(obj.nonceStr)\n                console.log(packageStr)\n                console.log(obj.nonceStr)\n                console.log(String(obj.timestamp))\n                console.log(obj.sign)\n\n                // 获取当前平台\n                const platform = this.getCurrentPlatform();\n                console.log('当前平台:', platform);\n\n                const paymentParams = {\n                  timeStamp: String(obj.timestamp), // 一定要是 string\n                  nonceStr: obj.nonceStr,\n                  package: \"prepay_id=\" + obj.prepayId,\n                  signType: 'MD5',\n                  paySign: obj.sign\n                };\n                console.log(JSON.stringify(paymentParams));\n\n                // 根据平台选择不同的支付方式\n                if (platform === 'app-plus') {\n                  // APP环境使用微信支付\n                  console.log('APP环境，使用微信支付');\n                  this.handleAppWechatPay(obj);\n                } else if (platform === 'mp-weixin') {\n                  // 微信小程序环境保持原有逻辑\n                  console.log('微信小程序环境，使用小程序支付');\n                  this.handleMiniProgramPay(obj);\n                } else {\n                  // 其他环境（H5等）\n                  console.log('其他环境，使用默认支付方式');\n                  this.handleMiniProgramPay(obj);\n                }\n              }\n\n            }).catch(err => {\n              console.error('升级失败:', err);\n              uni.showToast({\n                title: '升级失败',\n                icon: 'error'\n              });\n            });\n\n          }\n        }\n      });\n    },\n\n    // 获取升级路径说明文本\n    getUpgradePathText(currentGrade, targetGrade, sortedGrades) {\n      const currentIndex = sortedGrades.findIndex(grade => grade.id === currentGrade.id);\n      const targetIndex = sortedGrades.findIndex(grade => grade.id === targetGrade.id);\n\n      if (currentIndex === -1 || targetIndex === -1 || targetIndex <= currentIndex) {\n        return '';\n      }\n\n      const pathParts = [];\n      for (let i = currentIndex + 1; i <= targetIndex; i++) {\n        pathParts.push(`${sortedGrades[i].labelName}(¥${sortedGrades[i].earnestMoney})`);\n      }\n\n      return `升级路径：${pathParts.join(' → ')}`;\n    },\n    formatDate(dateString) {\n      if (!dateString) return ''\n      // 简单格式化日期，去掉时间部分\n      return dateString.split(' ')[0]\n    }\n  },\n  onLoad() {\n    // 先获取等级列表，再获取当前等级\n    this.refreshData()\n  },\n\n  // 下拉刷新\n  async onPullDownRefresh() {\n    try {\n      await this.refreshData();\n      uni.showToast({\n        title: '刷新成功',\n        icon: 'success',\n        duration: 1000\n      });\n    } catch (error) {\n      console.error('下拉刷新失败:', error);\n      uni.showToast({\n        title: '刷新失败，请重试',\n        icon: 'none'\n      });\n    } finally {\n      uni.stopPullDownRefresh();\n    }\n  }\n}\n</script>\n\n<style>\n.container {\n  padding: 20rpx;\n  background-color: #f5f5f5;\n  min-height: 100vh;\n}\n\n.current-grade {\n  background-color: #fff;\n  border-radius: 12rpx;\n  padding: 24rpx;\n  margin-bottom: 20rpx;\n  display: flex;\n  align-items: center;\n  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);\n}\n\n.current-label {\n  font-size: 28rpx;\n  color: #666;\n  margin-right: 10rpx;\n}\n\n.current-value {\n  font-size: 32rpx;\n  font-weight: bold;\n  color: #2d8cf0;\n  margin-right: 10rpx;\n}\n\n.current-id {\n  font-size: 24rpx;\n  color: #999;\n}\n\n.upgrade-title {\n  font-size: 28rpx;\n  color: #333;\n  font-weight: bold;\n  margin: 20rpx 0;\n  padding-left: 10rpx;\n}\n\n.grade-list {\n  display: flex;\n  flex-direction: column;\n  gap: 20rpx;\n}\n\n.grade-item {\n  background-color: #fff;\n  border-radius: 12rpx;\n  padding: 24rpx;\n  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);\n  position: relative;\n}\n\n.current-grade-item {\n  border: 2rpx solid #2d8cf0;\n  background-color: #f0f9ff;\n}\n\n.grade-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20rpx;\n  padding-bottom: 20rpx;\n  border-bottom: 1rpx solid #eee;\n}\n\n.grade-name {\n  font-size: 32rpx;\n  font-weight: bold;\n  color: #333;\n}\n\n.grade-id {\n  font-size: 24rpx;\n  color: #999;\n}\n\n.grade-details {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 16rpx;\n  margin-bottom: 20rpx;\n}\n\n.detail-item {\n  display: flex;\n  flex-direction: column;\n}\n\n.detail-label {\n  font-size: 24rpx;\n  color: #666;\n  margin-bottom: 4rpx;\n}\n\n.detail-value {\n  font-size: 28rpx;\n  color: #333;\n  font-weight: 500;\n}\n\n.upgrade-price {\n  color: #f56c6c;\n  font-weight: bold;\n}\n\n.current-tag {\n  color: #67c23a;\n  font-weight: bold;\n}\n\n.upgrade-btn {\n  background-color: #2d8cf0;\n  color: white;\n  border: none;\n  border-radius: 8rpx;\n  padding: 12rpx 0;\n  /* 减少上下 padding，降低高度 */\n  font-size: 28rpx;\n  line-height: 1.2;\n  /* 调整行高，避免文字上下间距过大 */\n  margin-top: 10rpx;\n  width: 100%;\n  height: auto;\n  /* 确保高度自适应 */\n  min-height: 60rpx;\n  /* 设置最小高度（可选） */\n}\n\n.upgrade-btn[disabled] {\n  background-color: #c8c9cc;\n  color: #909399;\n}\n\n/* 根据不同的等级添加不同的颜色标识 */\n.grade-item:nth-child(1) .grade-name {\n  color: #ff6b81;\n  /* 钻石师傅 - 粉色 */\n}\n\n.grade-item:nth-child(2) .grade-name {\n  color: #ffa502;\n  /* 金牌师傅 - 金色 */\n}\n\n.grade-item:nth-child(3) .grade-name {\n  color: #a4b0be;\n  /* 银牌师傅 - 银色 */\n}\n\n.grade-item:nth-child(4) .grade-name {\n  color: #cd7f32;\n  /* 铜牌师傅 - 铜色 */\n}\n\n.grade-item:nth-child(5) .grade-name {\n  color: #57606f;\n  /* 普通师傅 - 灰色 */\n}\n</style>", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./shifuGrade.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./shifuGrade.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755334668663\n      var cssReload = require(\"E:/BaiduNetdiskDownload/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}