{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/App.vue?9af4", "uni-app:///App.vue", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/App.vue?c432", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/App.vue?c7ba"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "<PERSON><PERSON>", "use", "uView", "component", "upload", "prototype", "$navigateTo", "url", "uni", "navigateTo", "fail", "err", "console", "error", "showToast", "title", "errMsg", "icon", "$api", "api", "$util", "util", "config", "productionTip", "App", "mpType", "app", "store", "$mount", "data", "registrationID", "connectStatus", "mounted", "onLaunch", "configInfo", "$store", "key", "val", "arr", "commonOptions", "channel_id", "systemInfo", "platform", "appVersion", "appName", "primaryColor", "onShow", "onHide", "methods", "getRegistrationID", "getNotificationEnabled", "noticMsgTool", "getBaseConfig", "checkAppUpdateOnLaunch", "initLocation", "locationManager", "silent", "checkLocationUpdate", "cachedData"], "mappings": ";;;;;;;;;;;;;;AAAA;AAE2D;AAE3D;AACA;AAEA;AACA;AAIA;AAEA;AAA6B;AAAA;AAb7B;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAAC;EAAA;IAAA;EAAA;AAAA;AAc3DC,YAAG,CAACC,GAAG,CAACC,gBAAK,CAAC;AACdF,YAAG,CAACG,SAAS,CAAC,QAAQ,EAAEC,MAAM,CAAC;AAC/B;AACAJ,YAAG,CAACK,SAAS,CAACC,WAAW,GAAG,UAACC,GAAG,EAAK;EACnCC,GAAG,CAACC,UAAU,CAAC;IACbF,GAAG,EAAHA,GAAG;IACHG,IAAI,EAAE,cAACC,GAAG,EAAK;MACbC,OAAO,CAACC,KAAK,CAAC,OAAO,EAAEF,GAAG,CAAC;MAC3BH,GAAG,CAACM,SAAS,CAAC;QACZC,KAAK,EAAE,QAAQ,GAAGJ,GAAG,CAACK,MAAM;QAC5BC,IAAI,EAAE;MACR,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;AACJ,CAAC;AACDjB,YAAG,CAACK,SAAS,CAACa,IAAI,GAAGC,cAAG;AACxBnB,YAAG,CAACK,SAAS,CAACe,KAAK,GAAGC,eAAI;AAI1BrB,YAAG,CAACsB,MAAM,CAACC,aAAa,GAAG,KAAK;AAEhCC,YAAG,CAACC,MAAM,GAAG,KAAK;AAElB,IAAMC,GAAG,GAAG,IAAI1B,YAAG,iCACfwB,YAAG;EACNG,KAAK,EAALA;AAAK,GACJ;AACF,UAAAD,GAAG,EAACE,MAAM,EAAE,C;;;;;;;;;;;;;AC5CZ;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACuD;AACL;AACc;;;AAGhE;AACsN;AACtN,gBAAgB,8NAAU;AAC1B,EAAE,yEAAM;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAk1B,CAAgB,k2BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;ACCt2B;AACA;AACA;AACA;AAAA,eAOA;EACA;EACA;EACA;EACAC;IACA;MACAC;MACAC;IACA;EACA;EAEAC;IAAA;MAAA;QAAA;UAAA;YAAA;cASApB;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EACA;EAEAqB;IAAA;IAAA;MAAA;MAAA;QAAA;UAAA;YAAA;cACArB;cAKAA;cAiEAsB;cACA;gBACAC;kBACAC;kBACAC;gBACA;cACA;cAEAC;cACAA;gBACA;gBACA;kBACAH;oBACAC;oBACAC;kBACA;gBACA;cACA;cACAE;cAAA,wBAGAA,cADAC;cAEA;gBACAD;gBACAJ;kBACAC;kBACAC;gBACA;cACA;cACA;cACAzB;cACA;gBACA6B;gBACA7B;kBACA8B;kBACAC;kBACAC;gBACA;cACA;gBACAhC;cACA;cAWAA;cAAA,wBAKAuB,wCADAU;cAAA,KAEAA;gBAAA;gBAAA;cAAA;cAAA;YAAA;cAAA;cAAA,OACA;YAAA;cAEA;cACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EACA;EACAC;IAAA;IAAA;MAAA;QAAA;UAAA;YAAA;cACAlC;cACA;cACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EACA;EACAmC;IACAnC;EACA;EACAoC;IACA;IACAC,iDAaA;IAEA;IACAC,2DAqBA;IAEA;IACAC,uCAmDA;IAEAC;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACAlC;cAAA;gBAAAI;gBACA;kBACAA;gBACA;gBACA;kBACAA;gBACA;gBACAY;gBACAC;kBACAC;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;AACA;AACA;AACA;IACAgB;MAAA;QAAA;UAAA;YAAA;cAAA;gBA+CAzC;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;AACA;AACA;IACA0C;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA1C;gBACA;gBAAA;gBAAA,OACA2C;kBAAAC;gBAAA;cAAA;gBACA5C;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;AACA;AACA;IACA6C;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA;gBACAC;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBACA9C;gBAAA;gBAAA,OACA2C;kBAAAC;gBAAA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGA5C;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACpXA;AAAA;AAAA;AAAA;AAAikD,CAAgB,qhDAAG,EAAC,C;;;;;;;;;;;ACArlD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "common/main.js", "sourcesContent": ["import 'uni-pages';\r\n// @ts-ignore\r\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/uni-stat/dist/uni-stat.es.js';/* eslint-disable */\r\n\r\nimport Vue from 'vue'\r\nimport App from './App'\r\n// import './index.css'\r\nimport api from \"api/index.js\"\r\nimport util from \"@/utils/index.js\"\r\n\r\n\r\n\r\nimport store from \"@/store/index.js\"\r\nimport upload from '@/components/upload.vue'\r\nimport uView from \"uview-ui\";\r\n\r\nVue.use(uView);\r\nVue.component('upload', upload)\r\n// 挂载全局跳转方法\r\nVue.prototype.$navigateTo = (url) => {\r\n  uni.navigateTo({\r\n    url,\r\n    fail: (err) => {\r\n      console.error(\"跳转失败:\", err);\r\n      uni.showToast({\r\n        title: \"跳转失败: \" + err.errMsg,\r\n        icon: \"none\"\r\n      });\r\n    }\r\n  });\r\n};\r\nVue.prototype.$api = api\r\nVue.prototype.$util = util\r\n\r\n\r\n\r\nVue.config.productionTip = false\r\n\r\nApp.mpType = 'app'\r\n\r\nconst app = new Vue({\r\n\t...App,\r\n\tstore,\r\n})\r\napp.$mount()", "var render, staticRenderFns, recyclableRender, components\nvar renderjs\nimport script from \"./App.vue?vue&type=script&lang=js&\"\nexport * from \"./App.vue?vue&type=script&lang=js&\"\nimport style0 from \"./App.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"App.vue\"\nexport default component.exports", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./App.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./App.vue?vue&type=script&lang=js&\"", "<script>\n\timport $api from \"@/api/index.js\"\n\timport $store from \"@/store/index.js\"\n\timport appUpdate from \"@/utils/app-update.js\"\n\timport locationManager from \"@/utils/location-manager.js\"\n\n\t// #ifdef APP-PLUS\n\tvar jpushModule = uni.requireNativePlugin(\"JG-JPush\");\n\tvar audioObj = uni.getBackgroundAudioManager();\n\t// #endif\n\n\texport default {\n\t\t// globalData: {\n\t\t//     primaryColor: '#A40035', // Default fallback, matching your sidebar’s color\n\t\t//   },\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tregistrationID: '',\n\t\t\t\tconnectStatus: ''\n\t\t\t}\t\n\t\t},\n\n\t\tasync mounted() {\n\t\t\t// #ifdef H5\n\t\t\tif (typeof window.entryUrl === 'undefined' || window.entryUrl === '') {\n\t\t\t\twindow.entryUrl = window.location.href.split('#')[0]\n\t\t\t}\n\t\t\tif (window.location.href.indexOf('?#') < 0) {\n\t\t\t\twindow.location.href = window.location.href.replace(\"#\", \"?#\");\n\t\t\t}\n\t\t\t// #endif\n\t\t\tconsole.log('App mounted')\n\t\t},\n\t\n\t\tasync onLaunch() {\n\t\t\tconsole.log('=== App onLaunch 开始执行 ===')\n\t\t\t// #ifdef APP-PLUS\n\t\t\tconsole.log('=== 当前运行环境: APP-PLUS ===')\n\t\t\t// #endif\n\t\t\t// #ifndef APP-PLUS\n\t\t\tconsole.log('=== 当前运行环境: 非APP-PLUS ===')\n\t\t\t// #endif\n\t\t\t// #ifdef APP-PLUS\n\t\t\t// 检查极光推送模块是否可用\n\t\t\tif (jpushModule) {\n\t\t\t\ttry {\n\t\t\t\t\t// 初始化极光推送\n\t\t\t\t\tjpushModule.initJPushService();\n\t\t\t\t\tjpushModule.setLoggerEnable(true);\n\t\t\t\t\tplus.screen.lockOrientation(\"portrait-primary\");\n\n\t\t\t\t\t// 监听极光推送连接状态\n\t\t\t\t\tthis.getNotificationEnabled();\n\t\t\t\t\tjpushModule.addConnectEventListener(result => {\n\t\t\t\t\t\tlet connectEnable = result.connectEnable\n\t\t\t\t\t\tuni.$emit('connectStatusChange', connectEnable)\n\t\t\t\t\t});\n\n\t\t\t\t\t// 监听推送消息\n\t\t\t\t\tjpushModule.addNotificationListener(result => {\n\t\t\t\t\t\tjpushModule.setBadge(0);\n\t\t\t\t\t\tplus.runtime.setBadgeNumber(0);\n\t\t\t\t\t\tlet notificationEventType = result.notificationEventType\n\t\t\t\t\t\tconsole.log(\"通知\", result, notificationEventType)\n\n\t\t\t\t\t\t// 点击事件处理\n\t\t\t\t\t\tif (notificationEventType == 'notificationOpened') {\n\t\t\t\t\t\t\t// 这里可以根据推送内容进行页面跳转\n\t\t\t\t\t\t\tlet extras = result.extras || {};\n\t\t\t\t\t\t\tif (extras.page) {\n\t\t\t\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\t\t\t\turl: extras.page\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\n\t\t\t\t\t// 监听连接状态变化\n\t\t\t\t\tuni.$on('connectStatusChange', (connectStatus) => {\n\t\t\t\t\t\tvar connectStr = ''\n\t\t\t\t\t\tif (connectStatus == true) {\n\t\t\t\t\t\t\tconnectStr = '已连接'\n\t\t\t\t\t\t\tthis.getRegistrationID()\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tconnectStr = '未连接'\n\t\t\t\t\t\t}\n\t\t\t\t\t\tconsole.log('监听到了连接状态变化 --- ', connectStr)\n\t\t\t\t\t\tthis.connectStatus = connectStr\n\t\t\t\t\t})\n\n\t\t\t\t\t// 检查推送状态\n\t\t\t\t\tjpushModule.isPushStopped(res => {\n\t\t\t\t\t\tconst { code } = res\n\t\t\t\t\t\tconsole.log(res, '推送连接状态');\n\t\t\t\t\t})\n\n\t\t\t\t\tconsole.log('极光推送初始化成功');\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('极光推送初始化失败:', error);\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tconsole.warn('极光推送模块未找到，请检查插件配置或使用自定义调试基座');\n\t\t\t}\n\t\t\t// #endif\n\n\t\t\tlet configInfo = uni.getStorageSync('configInfo') || ''\n\t\t\tif (configInfo) {\n\t\t\t\t$store.commit('updateConfigItem', {\n\t\t\t\t\tkey: 'configInfo',\n\t\t\t\t\tval: configInfo\n\t\t\t\t})\n\t\t\t}\n\n\t\t\tlet arr = ['autograph', 'userInfo', 'location', 'appLogin']\n\t\t\tarr.map(key => {\n\t\t\t\tlet val = uni.getStorageSync(key) || ''\n\t\t\t\tif (val) {\n\t\t\t\t\t$store.commit('updateUserItem', {\n\t\t\t\t\t\tkey,\n\t\t\t\t\t\tval\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t})\n\t\t\tlet commonOptions = $store.state.user.commonOptions\n\t\t\tlet {\n\t\t\t\tchannel_id = 0\n\t\t\t} = commonOptions\n\t\t\tif (channel_id) {\n\t\t\t\tcommonOptions.channel_id = 0\n\t\t\t\t$store.commit('updateUserItem', {\n\t\t\t\t\tkey: 'commonOptions',\n\t\t\t\t\tval: commonOptions\n\t\t\t\t})\n\t\t\t}\n\t\t\t// 检查APP更新（启动时检查，有更新时弹窗提醒）\n\t\t\tconsole.log('=== 准备检查更新，当前平台信息 ===')\n\t\t\ttry {\n\t\t\t\tconst systemInfo = uni.getSystemInfoSync()\n\t\t\t\tconsole.log('系统信息:', {\n\t\t\t\t\tplatform: systemInfo.platform,\n\t\t\t\t\tappVersion: systemInfo.appVersion,\n\t\t\t\t\tappName: systemInfo.appName\n\t\t\t\t})\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('获取系统信息失败:', error)\n\t\t\t}\n\n\t\t\t// #ifdef APP-PLUS\n\t\t\tconsole.log('=== APP启动完成，准备延迟检查更新 ===')\n\t\t\tsetTimeout(() => {\n\t\t\t\tconsole.log('=== 3秒延迟结束，开始执行更新检查 ===')\n\t\t\t\tthis.checkAppUpdateOnLaunch()\n\t\t\t}, 3000) // 延迟3秒检查，避免影响启动速度\n\t\t\t// #endif\n\n\t\t\t// #ifndef APP-PLUS\n\t\t\tconsole.log('=== 非APP环境，跳过更新检查 ===')\n\t\t\t// #endif\n\n\t\t\tlet {\n\t\t\t\tprimaryColor = ''\n\t\t\t} = $store.state.config.configInfo\n\t\t\tif (primaryColor) return\n\t\t\tawait this.getBaseConfig()\n\n\t\t\t// 初始化定位\n\t\t\tthis.initLocation()\n\t\t},\n\t\tasync onShow() {\n\t\t\tconsole.log('App Show')\n\t\t\t// 应用从后台回到前台时，检查定位是否需要更新\n\t\t\tthis.checkLocationUpdate()\n\t\t},\n\t\tonHide() {\n\t\t\tconsole.log('App Hide')\n\t\t},\n\t\tmethods: {\n\t\t\t// 获取registrationID\n\t\t\tgetRegistrationID() {\n\t\t\t\t// #ifdef APP-PLUS\n\t\t\t\tif (jpushModule) {\n\t\t\t\t\tjpushModule.getRegistrationID(result => {\n\t\t\t\t\t\tlet registerID = result.registerID\n\t\t\t\t\t\tconsole.log('registrationID:', registerID)\n\t\t\t\t\t\tthis.registrationID = registerID\n\t\t\t\t\t\tuni.setStorageSync(\"registerID\", registerID)\n\t\t\t\t\t})\n\t\t\t\t} else {\n\t\t\t\t\tconsole.warn('极光推送模块未找到，无法获取registrationID');\n\t\t\t\t}\n\t\t\t\t// #endif\n\t\t\t},\n\n\t\t\t// 检查通知权限\n\t\t\tgetNotificationEnabled() {\n\t\t\t\t// #ifdef APP-PLUS\n\t\t\t\tif (jpushModule) {\n\t\t\t\t\tif (uni.getSystemInfoSync().platform == \"ios\") {\n\t\t\t\t\t\tjpushModule.requestNotificationAuthorization((result) => {\n\t\t\t\t\t\t\tlet status = result.status\n\t\t\t\t\t\t\tif (status < 2) {\n\t\t\t\t\t\t\t\tthis.noticMsgTool()\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t})\n\t\t\t\t\t} else {\n\t\t\t\t\t\tjpushModule.isNotificationEnabled((result) => {\n\t\t\t\t\t\t\tif (result.code == 0) {\n\t\t\t\t\t\t\t\tthis.noticMsgTool()\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t})\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tconsole.warn('极光推送模块未找到，无法检查通知权限');\n\t\t\t\t}\n\t\t\t\t// #endif\n\t\t\t},\n\n\t\t\t// 引导用户开启通知权限\n\t\t\tnoticMsgTool() {\n\t\t\t\t// #ifdef APP-PLUS\n\t\t\t\tif (uni.getSystemInfoSync().platform == \"ios\") {\n\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\ttitle: '通知权限开启提醒',\n\t\t\t\t\t\tcontent: '您还没有开启通知权限，无法接受到消息通知，请前往设置！',\n\t\t\t\t\t\tshowCancel: false,\n\t\t\t\t\t\tconfirmText: '去设置',\n\t\t\t\t\t\tsuccess: function(res) {\n\t\t\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t\t\tvar app = plus.ios.invoke('UIApplication', 'sharedApplication');\n\t\t\t\t\t\t\t\tvar setting = plus.ios.invoke('NSURL', 'URLWithString:', 'app-settings:');\n\t\t\t\t\t\t\t\tplus.ios.invoke(app, 'openURL:', setting);\n\t\t\t\t\t\t\t\tplus.ios.deleteObject(setting);\n\t\t\t\t\t\t\t\tplus.ios.deleteObject(app);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t} else {\n\t\t\t\t\tvar main = plus.android.runtimeMainActivity();\n\t\t\t\t\tvar pkName = main.getPackageName();\n\t\t\t\t\tvar uid = main.getApplicationInfo().plusGetAttribute(\"uid\");\n\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\ttitle: '通知权限开启提醒',\n\t\t\t\t\t\tcontent: '您还没有开启通知权限，无法接受到消息通知，请前往设置！',\n\t\t\t\t\t\tshowCancel: false,\n\t\t\t\t\t\tconfirmText: '去设置',\n\t\t\t\t\t\tsuccess: function(res) {\n\t\t\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t\t\tvar Intent = plus.android.importClass('android.content.Intent');\n\t\t\t\t\t\t\t\tvar Build = plus.android.importClass(\"android.os.Build\");\n\t\t\t\t\t\t\t\tif (Build.VERSION.SDK_INT >= 26) {\n\t\t\t\t\t\t\t\t\tvar intent = new Intent('android.settings.APP_NOTIFICATION_SETTINGS');\n\t\t\t\t\t\t\t\t\tintent.putExtra('android.provider.extra.APP_PACKAGE', pkName);\n\t\t\t\t\t\t\t\t} else if (Build.VERSION.SDK_INT >= 21) {\n\t\t\t\t\t\t\t\t\tvar intent = new Intent('android.settings.APP_NOTIFICATION_SETTINGS');\n\t\t\t\t\t\t\t\t\tintent.putExtra(\"app_package\", pkName);\n\t\t\t\t\t\t\t\t\tintent.putExtra(\"app_uid\", uid);\n\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\tvar Settings = plus.android.importClass(\"android.provider.Settings\");\n\t\t\t\t\t\t\t\t\tvar Uri = plus.android.importClass(\"android.net.Uri\");\n\t\t\t\t\t\t\t\t\tintent.setAction(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);\n\t\t\t\t\t\t\t\t\tvar uri = Uri.fromParts(\"package\", pkName, null);\n\t\t\t\t\t\t\t\t\tintent.setData(uri);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tmain.startActivity(intent);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\t// #endif\n\t\t\t},\n\n\t\t\tasync getBaseConfig() {\n\t\t\t\tlet config = await $api.base.getConfig()\n\t\t\t\tif (!config.primaryColor) {\n\t\t\t\t\tconfig.primaryColor = '#A40035'\n\t\t\t\t}\n\t\t\t\tif (!config.subColor) {\n\t\t\t\t\tconfig.subColor = '#F1C06B'\n\t\t\t\t}\n\t\t\t\tlet configInfo = Object.assign($store.state.config.configInfo, config)\n\t\t\t\t$store.commit('updateConfigItem', {\n\t\t\t\t\tkey: 'configInfo',\n\t\t\t\t\tval: configInfo\n\t\t\t\t})\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * 启动时检查APP更新\n\t\t\t * 如果有更新则弹窗提醒，没有更新则静默处理\n\t\t\t */\n\t\t\tasync checkAppUpdateOnLaunch() {\n\t\t\t\t// #ifdef APP-PLUS\n\t\t\t\ttry {\n\t\t\t\t\tconsole.log('=== 开始检查APP更新 ===')\n\n\t\t\t\t\t// 获取当前版本\n\t\t\t\t\tconst currentVersion = await appUpdate.getCurrentVersion()\n\t\t\t\t\tconsole.log('当前版本:', currentVersion)\n\n\t\t\t\t\t// 调用后端接口检查更新\n\t\t\t\t\tconst response = await $api.user.checkAppVersion({\n\t\t\t\t\t\tversion: currentVersion,\n\t\t\t\t\t\tplatform: 2 // 用户端\n\t\t\t\t\t})\n\n\t\t\t\t\tconsole.log('版本检查响应:', JSON.stringify(response))\n\n\t\t\t\t\tif (response.code === '200' && response.data) {\n\t\t\t\t\t\tconst updateInfo = response.data\n\t\t\t\t\t\tconsole.log('更新信息:', JSON.stringify(updateInfo))\n\n\t\t\t\t\t\tif (updateInfo.needUpdate) {\n\t\t\t\t\t\t\tconsole.log('=== 发现新版本，显示更新提醒 ===')\n\t\t\t\t\t\t\t// 有更新时显示弹窗\n\t\t\t\t\t\t\tappUpdate.showUpdateDialog(updateInfo)\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tconsole.log('=== 已是最新版本，无需更新 ===')\n\t\t\t\t\t\t}\n\t\t\t\t\t} else {\n\t\t\t\t\t\tconsole.log('检查更新失败:', response.msg || '未知错误')\n\t\t\t\t\t\t// 如果是网络错误或服务器错误，可以尝试使用默认的更新检查\n\t\t\t\t\t\tconsole.log('尝试使用默认更新检查方法...')\n\t\t\t\t\t\tawait appUpdate.checkUpdate({ silent: true, showLoading: false })\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('检查更新异常:', error)\n\t\t\t\t\t// 如果出现异常，尝试使用默认的更新检查\n\t\t\t\t\ttry {\n\t\t\t\t\t\tconsole.log('异常后尝试使用默认更新检查方法...')\n\t\t\t\t\t\tawait appUpdate.checkUpdate({ silent: true, showLoading: false })\n\t\t\t\t\t} catch (fallbackError) {\n\t\t\t\t\t\tconsole.error('默认更新检查也失败:', fallbackError)\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t// #endif\n\n\t\t\t\t// #ifndef APP-PLUS\n\t\t\t\tconsole.log('非APP环境，跳过更新检查')\n\t\t\t\t// #endif\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * 初始化定位\n\t\t\t */\n\t\t\tasync initLocation() {\n\t\t\t\ttry {\n\t\t\t\t\tconsole.log('开始初始化定位...')\n\t\t\t\t\t// 静默获取定位，不显示loading\n\t\t\t\t\tawait locationManager.getLocation({ silent: true })\n\t\t\t\t\tconsole.log('定位初始化完成')\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('定位初始化失败:', error)\n\t\t\t\t\t// 定位失败不影响应用启动\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * 检查定位更新\n\t\t\t */\n\t\t\tasync checkLocationUpdate() {\n\t\t\t\ttry {\n\t\t\t\t\t// 检查是否需要更新定位（超过5分钟自动更新）\n\t\t\t\t\tconst cachedData = locationManager._getFromStorage()\n\t\t\t\t\tif (!cachedData || (Date.now() - cachedData.cacheTime > 5 * 60 * 1000)) {\n\t\t\t\t\t\tconsole.log('定位缓存过期，重新获取定位...')\n\t\t\t\t\t\tawait locationManager.getLocation({ silent: true })\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('检查定位更新失败:', error)\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\">\n\t@import \"uview-ui/index.scss\";\n\t@import \"/styles/index.wxss\";\n\n\t/* #ifdef H5 */\n\tuni-page-head {\n\t\tdisplay: none;\n\t}\n\n\t/* #endif */\n\tpage {\n\t\tfont-size: 28rpx;\n\t\tcolor: #222;\n\t\tline-height: 1.5;\n\t\tbackground: #fff;\n\t\tfont-family: 'MyFont';\n\t}\n\n\tinput {\n\t\t// font-family: PingFangSC-Medium, PingFang SC, -apple-system-font, Helvetica Neue, Helvetica, sans-serif;\n\t}\n\n\tinput::-webkit-input-placeholder {\n\t\t/* WebKit browsers */\n\t\tcolor: #A9A9A9;\n\t}\n\n\tinput:-moz-placeholder {\n\t\t/* Mozilla Firefox 4 to 18 */\n\t\tcolor: #A9A9A9;\n\t}\n\n\tinput::-moz-placeholder {\n\t\t/* Mozilla Firefox 19+ */\n\t\tcolor: #A9A9A9;\n\t}\n\n\tinput:-ms-input-placeholder {\n\t\t/* Internet Explorer 10+ */\n\t\tcolor: #A9A9A9;\n\t}\n\n\tview {\n\t\tbox-sizing: border-box;\n\t}\n\n\timage {\n\t\tdisplay: block;\n\t}\n\n\t/*隐藏滚动条*/\n\t::-webkit-scrollbar {\n\t\twidth: 0;\n\t\theight: 0;\n\t\tcolor: transparent;\n\t}\n\n\t/* #ifdef MP-BAIDU */\n\t.swan-button.swan-button-radius-ios {\n\t\tborder-radius: 0;\n\t}\n\n\t/* #endif */\n</style>", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./App.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./App.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755337032917\n      var cssReload = require(\"E:/BaiduNetdiskDownload/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}