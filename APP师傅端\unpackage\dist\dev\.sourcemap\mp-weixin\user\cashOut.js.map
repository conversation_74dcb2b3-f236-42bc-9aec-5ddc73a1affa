{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/user/cashOut.vue?c489", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/user/cashOut.vue?666f", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/user/cashOut.vue?db87", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/user/cashOut.vue?4f62", "uni-app:///user/cashOut.vue", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/user/cashOut.vue?86f1", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/user/cashOut.vue?8ac7"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "money", "allmoney", "isSubmitting", "mchId", "cashToType", "showCashToType", "alipayPhone", "realName", "idCode", "isWeixinMiniProgram", "onPullDownRefresh", "console", "setTimeout", "uni", "methods", "confirmTx", "amount", "title", "icon", "content", "confirmText", "cancelText", "success", "res", "params", "url", "goAll", "change", "checkPlatform", "getCurrentPlatform", "buildWithdrawParams", "type", "getCashToTypeName", "selectCashToType", "getMoney", "copyPhoneNumber", "fail", "onLoad"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgI;AAChI;AAC2D;AACL;AACsC;;;AAG5F;AACsN;AACtN,gBAAgB,8NAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,8FAAM;AACR,EAAE,uGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,aAAa,4RAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC1DA;AAAA;AAAA;AAAA;AAAs1B,CAAgB,s2BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCoG12B;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;IACA;EACA;EACAC;IACAC;IACAC;MACAC;IACA;EACA;EACAC;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAC;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBACAH;kBACAI;kBACAC;gBACA;gBAAA;cAAA;gBAAA,MAGAF;kBAAA;kBAAA;gBAAA;gBACAH;kBACAI;kBACAC;gBACA;gBAAA;cAAA;gBAAA,MAGAF;kBAAA;kBAAA;gBAAA;gBACAH;kBACAI;kBACAC;gBACA;gBAAA;cAAA;gBAAA,MAGAF;kBAAA;kBAAA;gBAAA;gBACAH;kBACAI;kBACAC;gBACA;gBAAA;cAAA;gBAAA,MAKA;kBAAA;kBAAA;gBAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBACAL;kBACAI;kBACAC;gBACA;gBAAA;cAAA;gBAAA,IAGA;kBAAA;kBAAA;gBAAA;gBACAL;kBACAI;kBACAC;gBACA;gBAAA;cAAA;gBAAA,IAGA;kBAAA;kBAAA;gBAAA;gBACAL;kBACAI;kBACAC;gBACA;gBAAA;cAAA;gBAKA;gBACAL;kBACAI;kBACAE;kBACAC;kBACAC;kBACAC;oBAAA;sBAAA;sBAAA;wBAAA;0BAAA;4BAAA;8BACAX;8BAAA,KACAY;gCAAA;gCAAA;8BAAA;8BAAA;8BAWA;;8BAEA;8BACAC,sCAEA;8BAAA;8BAAA,OACA;4BAAA;8BAAAD;8BAEA;gCACAV;kCACAI;kCACAC;gCACA;gCACA;gCACA;gCACAN;kCACAC;oCACAY;kCACA;gCACA;8BACA;gCACAZ;kCACAI;kCACAC;gCACA;8BACA;8BAAA;8BAAA;4BAAA;8BAAA;8BAAA;8BAGAL;gCACAI;gCACAC;8BACA;8BACA;4BAAA;4BAAA;8BAAA;0BAAA;wBAAA;sBAAA;oBAAA,CAGA;oBAAA;sBAAA;oBAAA;oBAAA;kBAAA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAQ;MACA;IACA;IACAC;MACA;IAAA,CACA;IAEA;IACAC;MAEA;MACA;MACA;IAYA;IAEA;IACAC;MAKA;MAKA;IACA;IAEA;IACAC;MACA;MACA;;MAEA;MACA;QACAd;QACAe;QAAA;QACA3B;MACA;;MAEA;MACA;QACA;QACA;MAAA,CACA;QACA;QACAoB;QACAA;QACAA;MACA;QACA;QACA;MAAA;MAGA;IACA;IAEA;IACAQ;MACA;QACA;UAAA;QACA;UAAA;QACA;UAAA;QACA;UAAA;MAAA;IAEA;IAEA;IACAC;MACA;MACA;;MAEA;MACA;QACA;QACA;QACA;MACA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAX;gBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAV;kBACAI;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACAiB;MACAtB;QACAd;QACAuB;UACAT;YACAI;YACAC;UACA;QACA;QACAkB;UACAvB;YACAI;YACAC;UACA;QACA;MACA;IACA;EACA;EACAmB;IACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACxWA;AAAA;AAAA;AAAA;AAA6lD,CAAgB,ijDAAG,EAAC,C;;;;;;;;;;;ACAjnD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "user/cashOut.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/uni-stat/dist/uni-stat.es.js';\nimport Vue from 'vue'\nimport Page from './user/cashOut.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./cashOut.vue?vue&type=template&id=9870f036&scoped=true&\"\nvar renderjs\nimport script from \"./cashOut.vue?vue&type=script&lang=js&\"\nexport * from \"./cashOut.vue?vue&type=script&lang=js&\"\nimport style0 from \"./cashOut.vue?vue&type=style&index=0&id=9870f036&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"9870f036\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"user/cashOut.vue\"\nexport default component.exports", "export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./cashOut.vue?vue&type=template&id=9870f036&scoped=true&\"", "var components\ntry {\n  components = {\n    \"u-Input\": function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u--input/u--input\" */ \"uview-ui/components/u--input/u--input.vue\"\n      )\n    },\n    uPopup: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-popup/u-popup\" */ \"uview-ui/components/u-popup/u-popup.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.getCashToTypeName()\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.showCashToType = true\n    }\n    _vm.e1 = function ($event) {\n      _vm.showCashToType = false\n    }\n    _vm.e2 = function ($event) {\n      _vm.showCashToType = false\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./cashOut.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./cashOut.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"page\">\n\t\t<view class=\"header\">\n\t\t\t<view class=\"left\">提现至</view>\n\t\t\t<view class=\"right\" @tap=\"showCashToType = true\">\n\t\t\t\t{{ getCashToTypeName() }}\n\t\t\t\t<text class=\"arrow\">></text>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 支付宝提现信息输入 -->\n\t\t<view class=\"alipay-info\" v-if=\"cashToType === 2\">\n\t\t\t<view class=\"info-item\">\n\t\t\t\t<view class=\"label\">支付宝手机号</view>\n\t\t\t\t<u--input\n\t\t\t\t\tplaceholder=\"请输入支付宝登录手机号\"\n\t\t\t\t\tv-model=\"alipayPhone\"\n\t\t\t\t\tborder=\"none\"\n\t\t\t\t></u--input>\n\t\t\t</view>\n\t\t\t<view class=\"info-item\">\n\t\t\t\t<view class=\"label\">真实姓名</view>\n\t\t\t\t<u--input\n\t\t\t\t\tplaceholder=\"请输入真实姓名\"\n\t\t\t\t\tv-model=\"realName\"\n\t\t\t\t\tborder=\"none\"\n\t\t\t\t></u--input>\n\t\t\t</view>\n\t\t\t<view class=\"info-item\">\n\t\t\t\t<view class=\"label\">身份证号</view>\n\t\t\t\t<u--input\n\t\t\t\t\tplaceholder=\"请输入身份证号\"\n\t\t\t\t\tv-model=\"idCode\"\n\t\t\t\t\tborder=\"none\"\n\t\t\t\t></u--input>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<view class=\"mid\">\n\t\t\t<view class=\"title\">提现金额</view>\n\t\t\t<view class=\"top\">\n\t\t\t\t<view class=\"t_left\">\n\t\t\t\t\t<u--input\n\t\t\t\t\t\tplaceholder=\"请输入提现金额\"\n\t\t\t\t\t\ttype=\"digit\"\n\t\t\t\t\t\tborder=\"none\"\n\t\t\t\t\t\tv-model=\"money\"\n\t\t\t\t\t\t@change=\"change\"\n\t\t\t\t\t\tprefixIcon=\"rmb\"\n\t\t\t\t\t></u--input>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"r_left\" @tap=\"goAll\">全部提现</view>\n\t\t\t</view>\n\t\t\t<view class=\"bottom\">可提现金额￥{{ allmoney }}</view>\n\t\t</view>\n\t\t<view class=\"btn\" @tap=\"confirmTx\" :disabled=\"isSubmitting\">确认提现</view>\n\t\t<text class=\"tips\">温馨提示：提现申请发起后，预计3个工作日内到账。</text>\n\t\t<text class=\"contact\">有问题请联系客服 <text class=\"phone\" @tap=\"copyPhoneNumber\">4008326986</text></text>\n\n\t\t<!-- 提现渠道选择弹窗 -->\n\t\t<u-popup :show=\"showCashToType\" mode=\"bottom\" :round=\"10\" closeable @close=\"showCashToType = false\">\n\t\t\t<view class=\"cash-type-modal\">\n\t\t\t\t<view class=\"modal-header\">\n\t\t\t\t\t<view class=\"modal-title\">选择提现渠道</view>\n\t\t\t\t\t<view class=\"modal-close\" @tap=\"showCashToType = false\"></view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"cash-type-list\">\n\t\t\t\t\t<!-- 微信小程序环境下显示微信提现选项 -->\n\t\t\t\t\t<view\n\t\t\t\t\t\tv-if=\"isWeixinMiniProgram\"\n\t\t\t\t\t\tclass=\"cash-type-item\"\n\t\t\t\t\t\t:class=\"{ active: cashToType === 1 }\"\n\t\t\t\t\t\t@tap=\"selectCashToType(1)\"\n\t\t\t\t\t>\n\t\t\t\t\t\t<view class=\"type-name\">微信</view>\n\t\t\t\t\t\t<view class=\"type-check\" v-if=\"cashToType === 1\">✓</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view\n\t\t\t\t\t\tclass=\"cash-type-item\"\n\t\t\t\t\t\t:class=\"{ active: cashToType === 2 }\"\n\t\t\t\t\t\t@tap=\"selectCashToType(2)\"\n\t\t\t\t\t>\n\t\t\t\t\t\t<view class=\"type-name\">支付宝</view>\n\t\t\t\t\t\t<view class=\"type-check\" v-if=\"cashToType === 2\">✓</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view\n\t\t\t\t\t\tclass=\"cash-type-item\"\n\t\t\t\t\t\t:class=\"{ active: cashToType === 3 }\"\n\t\t\t\t\t\t@tap=\"selectCashToType(3)\"\n\t\t\t\t\t>\n\t\t\t\t\t\t<view class=\"type-name\">银行卡</view>\n\t\t\t\t\t\t<view class=\"type-check\" v-if=\"cashToType === 3\">✓</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</u-popup>\n\t</view>\n</template>\n\n<script>\nexport default {\n\tdata() {\n\t\treturn {\n\t\t\tmoney: '',\n\t\t\tallmoney: '0',\n\t\t\tisSubmitting: false,\n\t\t\tmchId: '1648027588', // Replace with your actual Merchant ID\n\t\t\tcashToType: 2, // 提现渠道：1=微信，2=支付宝，3=银行卡\n\t\t\tshowCashToType: false, // 是否显示提现渠道选择弹窗\n\t\t\talipayPhone: '', // 支付宝登录手机号\n\t\t\trealName: '', // 真实姓名\n\t\t\tidCode: '', // 身份证号\n\t\t\tisWeixinMiniProgram: false, // 是否为微信小程序环境\n\t\t};\n\t},\n\tonPullDownRefresh() {\n\t\tconsole.log('refresh');\n\t\tsetTimeout(function () {\n\t\t\tuni.stopPullDownRefresh();\n\t\t}, 1000);\n\t},\n\tmethods: {\n\t\tasync confirmTx() {\n\t\t\tconst amount = Number(this.money);\n\t\t\tif (!amount || amount <= 0) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '请输入有效的提现金额',\n\t\t\t\t\ticon: 'none',\n\t\t\t\t});\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tif (amount > Number(this.allmoney)) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '超过可提现金额',\n\t\t\t\t\ticon: 'none',\n\t\t\t\t});\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tif (amount > 800) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '最高提现金额为799元',\n\t\t\t\t\ticon: 'none',\n\t\t\t\t});\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tif (amount < 1) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '最低提现金额为1元',\n\t\t\t\t\ticon: 'none',\n\t\t\t\t});\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// 支付宝提现时验证必填信息\n\t\t\tif (this.cashToType === 2) {\n\t\t\t\tif (!this.alipayPhone) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '请输入支付宝登录手机号',\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tif (!this.realName) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '请输入真实姓名',\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tif (!this.idCode) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '请输入身份证号',\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// Show confirmation modal before proceeding\n\t\t\tuni.showModal({\n\t\t\t\ttitle: '确认提现',\n\t\t\t\tcontent: '为确保您的账户余额准确无误，提现操作一旦提交，请不要中途退出或刷新页面，若您在提现过程中中止操作，可能会导致余额错误，需等待1-3个工作日处理您的请求。',\n\t\t\t\tconfirmText: '确定',\n\t\t\t\tcancelText: '取消',\n\t\t\t\tsuccess: async (res) => {\n\t\t\t\t\tconsole.log(res)\n\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t// Proceed with withdrawal only if user confirms\n\t\t\t\t\t\t// if (!uni.canIUse('requestMerchantTransfer')) {\n\t\t\t\t\t\t// \tuni.showModal({\n\t\t\t\t\t\t// \t\tcontent: '你的微信版本过低，请更新至最新版本。',\n\t\t\t\t\t\t// \t\tshowCancel: false,\n\t\t\t\t\t\t// \t});\n\t\t\t\t\t\t// \treturn;\n\t\t\t\t\t\t// }\n\n\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\tthis.isSubmitting = true;\n\n\t\t\t\t\t\t\t// 构建请求参数\n\t\t\t\t\t\t\tconst params = this.buildWithdrawParams();\n\n\t\t\t\t\t\t\t// Request signed package from backend\n\t\t\t\t\t\t\tconst res = await this.$api.mine.applyWallet(params);\n\n\t\t\t\t\t\t\tif(res.code==='200'){\n\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\ttitle: res.msg || '提现申请提交成功',\n\t\t\t\t\t\t\t\t\ticon: 'success',\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\tthis.getMoney();\n\t\t\t\t\t\t\t\t// 提现成功后跳转到提现记录页面\n\t\t\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\t\t\t\t\turl: '/user/coreWallet'\n\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t}, 1500);\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\ttitle: res.msg || '提现申请失败',\n\t\t\t\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t} catch (error) {\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ttitle: '请稍后重试',\n\t\t\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\tthis.isSubmitting = false;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t});\n\t\t},\n\t\tgoAll() {\n\t\t\tthis.money = this.allmoney;\n\t\t},\n\t\tchange() {\n\t\t\t// Handle input change if needed\n\t\t},\n\n\t\t// 检测平台环境\n\t\tcheckPlatform() {\n\t\t\t// #ifdef MP-WEIXIN\n\t\t\tthis.isWeixinMiniProgram = true;\n\t\t\t// 微信小程序环境下，默认选择微信提现\n\t\t\tthis.cashToType = 1;\n\t\t\t// #endif\n\t\t\t// #ifdef APP-PLUS\n\t\t\tthis.isWeixinMiniProgram = false;\n\t\t\t// APP环境下，默认选择支付宝提现\n\t\t\tthis.cashToType = 2;\n\t\t\t// #endif\n\t\t\t// #ifdef H5\n\t\t\tthis.isWeixinMiniProgram = false;\n\t\t\t// H5环境下，默认选择支付宝提现\n\t\t\tthis.cashToType = 2;\n\t\t\t// #endif\n\t\t},\n\n\t\t// 获取当前平台类型\n\t\tgetCurrentPlatform() {\n\t\t\t// #ifdef APP-PLUS\n\t\t\treturn 'app-plus';\n\t\t\t// #endif\n\t\t\t// #ifdef MP-WEIXIN\n\t\t\treturn 'mp-weixin';\n\t\t\t// #endif\n\t\t\t// #ifdef H5\n\t\t\treturn 'h5';\n\t\t\t// #endif\n\t\t\treturn 'unknown';\n\t\t},\n\n\t\t// 构建提现参数\n\t\tbuildWithdrawParams() {\n\t\t\tconst platform = this.getCurrentPlatform();\n\t\t\tconst isApp = platform === 'app-plus';\n\n\t\t\t// 基础参数\n\t\t\tconst params = {\n\t\t\t\tamount: this.money,\n\t\t\t\ttype: 4, // 用户分销\n\t\t\t\tcashToType: this.cashToType\n\t\t\t};\n\n\t\t\t// 根据提现渠道添加额外参数\n\t\t\tif (this.cashToType === 1) {\n\t\t\t\t// 微信提现：仅在微信小程序环境下可用\n\t\t\t\t// 不需要额外参数\n\t\t\t} else if (this.cashToType === 2) {\n\t\t\t\t// 支付宝提现：需要传手机号、姓名、身份证号\n\t\t\t\tparams.phone = this.alipayPhone;\n\t\t\t\tparams.name = this.realName;\n\t\t\t\tparams.idCode = this.idCode;\n\t\t\t} else if (this.cashToType === 3) {\n\t\t\t\t// 银行卡提现（线下转账）\n\t\t\t\t// 不需要额外参数\n\t\t\t}\n\n\t\t\treturn params;\n\t\t},\n\n\t\t// 获取提现渠道名称\n\t\tgetCashToTypeName() {\n\t\t\tswitch (this.cashToType) {\n\t\t\t\tcase 1: return '微信';\n\t\t\t\tcase 2: return '支付宝';\n\t\t\t\tcase 3: return '银行卡';\n\t\t\t\tdefault: return this.isWeixinMiniProgram ? '微信' : '支付宝';\n\t\t\t}\n\t\t},\n\n\t\t// 选择提现渠道\n\t\tselectCashToType(type) {\n\t\t\tthis.cashToType = type;\n\t\t\tthis.showCashToType = false;\n\n\t\t\t// 切换渠道时清空支付宝信息\n\t\t\tif (type !== 2) {\n\t\t\t\tthis.alipayPhone = '';\n\t\t\t\tthis.realName = '';\n\t\t\t\tthis.idCode = '';\n\t\t\t}\n\t\t},\n\t\tasync getMoney() {\n\t\t\ttry {\n\t\t\t\tconst res = await this.$api.service.seeTuiMoney();\n\t\t\t\tthis.allmoney = res.data.totalCash || '0';\n\t\t\t\tthis.money = this.allmoney;\n\t\t\t} catch (error) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '获取可提现金额失败',\n\t\t\t\t\ticon: 'none',\n\t\t\t\t});\n\t\t\t}\n\t\t},\n\t\tcopyPhoneNumber() {\n\t\t\tuni.setClipboardData({\n\t\t\t\tdata: '4008326986',\n\t\t\t\tsuccess: () => {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '客服电话已复制',\n\t\t\t\t\t\ticon: 'success',\n\t\t\t\t\t});\n\t\t\t\t},\n\t\t\t\tfail: () => {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '复制失败，请稍后重试',\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t},\n\tonLoad() {\n\t\tthis.checkPlatform();\n\t\tthis.getMoney();\n\t},\n};\n</script>\n\n<style scoped lang=\"scss\">\n.page {\n\tbackground-color: #f8f8f8;\n\tmin-height: 100vh;\n\tpadding: 20rpx 0;\n\t.header {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: space-between;\n\t\tfont-size: 28rpx;\n\t\tfont-weight: 500;\n\t\tcolor: #3b3b3b;\n\t\tpadding: 0 30rpx;\n\t\twidth: 750rpx;\n\t\theight: 118rpx;\n\t\tbackground: #ffffff;\n\t\t.right {\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\tcolor: #2e80fe;\n\t\t\t.arrow {\n\t\t\t\tmargin-left: 10rpx;\n\t\t\t\tfont-size: 24rpx;\n\t\t\t}\n\t\t}\n\t}\n\n\t.alipay-info {\n\t\tmargin-top: 20rpx;\n\t\tbackground: #ffffff;\n\t\tpadding: 30rpx;\n\t\t.info-item {\n\t\t\tmargin-bottom: 30rpx;\n\t\t\t&:last-child {\n\t\t\t\tmargin-bottom: 0;\n\t\t\t}\n\t\t\t.label {\n\t\t\t\tfont-size: 28rpx;\n\t\t\t\tfont-weight: 500;\n\t\t\t\tcolor: #3b3b3b;\n\t\t\t\tmargin-bottom: 20rpx;\n\t\t\t}\n\t\t}\n\t}\n\t.mid {\n\t\tmargin-top: 20rpx;\n\t\twidth: 750rpx;\n\t\theight: 276rpx;\n\t\tbackground: #ffffff;\n\t\tpadding: 0 30rpx;\n\t\tpadding-top: 40rpx;\n\t\t.title {\n\t\t\tfont-size: 28rpx;\n\t\t\tfont-weight: 500;\n\t\t\tcolor: #3b3b3b;\n\t\t}\n\t\t.top {\n\t\t\tdisplay: flex;\n\t\t\talign-items: flex-end;\n\t\t\tjustify-content: space-between;\n\t\t\tpadding-top: 28rpx;\n\t\t\tpadding-bottom: 20rpx;\n\t\t\tborder-bottom: 2rpx solid #f2f3f6;\n\t\t\t.r_left {\n\t\t\t\tfont-size: 28rpx;\n\t\t\t\tfont-weight: 500;\n\t\t\t\tcolor: #e51837;\n\t\t\t}\n\t\t}\n\t\t.bottom {\n\t\t\tpadding-top: 20rpx;\n\t\t\tfont-size: 24rpx;\n\t\t\tfont-weight: 500;\n\t\t\tcolor: #999999;\n\t\t}\n\t}\n\t.btn {\n\t\tmargin: 60rpx auto 0;\n\t\twidth: 690rpx;\n\t\theight: 98rpx;\n\t\tbackground: #2e80fe;\n\t\tborder-radius: 50rpx;\n\t\tfont-size: 32rpx;\n\t\tfont-weight: 500;\n\t\tcolor: #ffffff;\n\t\tline-height: 98rpx;\n\t\ttext-align: center;\n\t\t&:disabled {\n\t\t\tbackground: #cccccc;\n\t\t}\n\t}\n\t.tips {\n\t\tdisplay: block;\n\t\tfont-size: 24rpx;\n\t\tfont-weight: 500;\n\t\tcolor: #999999;\n\t\ttext-align: center;\n\t\tmargin-top: 20rpx;\n\t}\n\t.contact {\n\t\tdisplay: block;\n\t\tfont-size: 24rpx;\n\t\tfont-weight: 500;\n\t\tcolor: #999999;\n\t\ttext-align: center;\n\t\tmargin-top: 20rpx;\n\t\t.phone {\n\t\t\tcolor: #2e80fe;\n\t\t\ttext-decoration: underline;\n\t\t}\n\t}\n}\n\n.cash-type-modal {\n\tbackground: #ffffff;\n\tborder-radius: 20rpx 20rpx 0 0;\n\tpadding: 40rpx 30rpx;\n\n\t.modal-header {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: space-between;\n\t\tmargin-bottom: 40rpx;\n\n\t\t.modal-title {\n\t\t\tfont-size: 32rpx;\n\t\t\tfont-weight: 600;\n\t\t\tcolor: #333333;\n\t\t}\n\n\t\t.modal-close {\n\t\t\tfont-size: 40rpx;\n\t\t\tcolor: #999999;\n\t\t\twidth: 60rpx;\n\t\t\theight: 60rpx;\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\tjustify-content: center;\n\t\t}\n\t}\n\n\t.cash-type-list {\n\t\t.cash-type-item {\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\tjustify-content: space-between;\n\t\t\tpadding: 30rpx 0;\n\t\t\tborder-bottom: 1rpx solid #f5f5f5;\n\n\t\t\t&:last-child {\n\t\t\t\tborder-bottom: none;\n\t\t\t}\n\n\t\t\t&.active {\n\t\t\t\t.type-name {\n\t\t\t\t\tcolor: #2e80fe;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.type-name {\n\t\t\t\tfont-size: 30rpx;\n\t\t\t\tcolor: #333333;\n\t\t\t}\n\n\t\t\t.type-check {\n\t\t\t\tfont-size: 32rpx;\n\t\t\t\tcolor: #2e80fe;\n\t\t\t\tfont-weight: bold;\n\t\t\t}\n\t\t}\n\t}\n}\n</style>", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./cashOut.vue?vue&type=style&index=0&id=9870f036&scoped=true&lang=scss&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./cashOut.vue?vue&type=style&index=0&id=9870f036&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755334680437\n      var cssReload = require(\"E:/BaiduNetdiskDownload/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}