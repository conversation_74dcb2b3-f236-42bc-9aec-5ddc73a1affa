<template>
	<view class="page">
		<u-picker :show="show" :columns="columns" @cancel="show = false" @confirm="confirmType"
			keyName="title"></u-picker>
		<u-picker :show="showCity" ref="uPicker" :loading="loading" :columns="columnsCity" @change="changeHandler"
			keyName="title" @cancel="showCity = false" @confirm="confirmCity"></u-picker>
		<view class="header" :style="'color:'+arr[status].color" v-if="status !== ''">{{arr[status].text}}</view>
		<view class="main">
			<view class="main_item">
				<view class="title"><span>*</span>法人姓名</view>
				<input type="text" v-model="form.legalPersonName" placeholder="请输入姓名">
			</view>
			<view class="main_item">
				<view class="title"><span>*</span>法人身份证号</view>
				<input type="text" v-model="form.legalPersonIdCard" placeholder="请输入身份证号">
			</view>
			<view class="main_item">
				<view class="title"><span>*</span>联系电话</view>
				<input type="text" v-model="form.legalPersonTel" placeholder="请输入联系电话">
			</view>
			<view class="main_item">
				<view class="title"><span>*</span>选择省市区代理</view>
				<input type="text" v-model="form.typename" placeholder="请选择代理级别" disabled @click="show = true">
			</view>
			<view class="main_item">
				<view class="title"><span>*</span>选择区域</view>
				<input type="text" v-model="form.city" placeholder="请选择代理区域" disabled @click="showCity = true" >
			</view>
			<view class="main_item">
				<view class="title"><span>*</span>上传法人身份证照片</view>
				<view class="card">
					<view class="card_item">
						<view class="top">
							<view class="das">
								<view class="up">
									<upload @upload="imgUpload" :imagelist="form.legalPersonIdCardImg1"
										imgtype="legalPersonIdCardImg1" imgclass="id_card_box" text="身份证人像面"
										:imgsize="1"></upload>
								</view>
							</view>
						</view>
						<view class="bottom">拍摄人像面</view>
					</view>
					<view class="card_item">
						<view class="top">
							<view class="das">
								<view class="up">
									<upload @upload="imgUpload" :imagelist="form.legalPersonIdCardImg2"
										imgtype="legalPersonIdCardImg2" imgclass="id_card_box" text="身份证国徽面"
										:imgsize="1"></upload>
								</view>
							</view>
						</view>
						<view class="bottom">拍摄国徽面</view>
					</view>
				</view>
			</view>
			<view class="main_item">
				<view class="title"><span>*</span>上传营业执照照片</view>
				<view class="big">
					<view class="top">
						<view class="das">
							<view class="up">
								<upload @upload="imgUpload" :imagelist="form.legalPersonLicense"
									imgtype="legalPersonLicense" imgclass="id_yy_box" text="营业执照" :imgsize="1">
								</upload>
							</view>
						</view>
					</view>
					<view class="bottom">
						拍摄营业执照
					</view>
				</view>
			</view>
		</view>
		<view class="footer">
			<view class="btn" @click="submit" v-if="status !== 1 && status !== 0">立即提交</view>
		</view>
	</view>
</template>

<script>
	// import upload from '@/user/components/upload.vue';
	export default {
		// components: {
		//     upload
		//   },
		data() {
			return {
				status:'',
				loading: false,
				applyInfo: null,
				arr: [{
					text: '信息审核中，请稍作等待',
					color: '#FE921B'
				},  {
					text: '审核成功',
					color: '#07C160'
				},{
					text: '审核失败',
					color: '#E72427'
				},],
				form: {
					typename: '省级',
					type: 1,
					city: "",
					cityId: [],
					legalPersonName: '',
					legalPersonIdCard: '',
					legalPersonTel: '',
					legalPersonIdCardImg1: [],
					legalPersonIdCardImg2: [],
					legalPersonLicense: []
				},
				showMoney: false,
				show: false,
				columns: [
					[{
						title: '省级',
						value: '1'
					}, {
						title: '市级',
						value: '2'
					}, {
						title: '区/县级代理',
						value: '3'
					}]
				],
				columnsCity: [
					[]
				],
				showCity: false
			}
		},
		watch: {
			"form.type": {
				handler(nval) {
					this.form.city = '',
						this.form.cityId = []

				}
			}
		},
		methods: {
			getcity(e) {
				this.loading = true
				this.$api.service.getCity(e).then(response => {
					console.log('getcity API response:', response)
					// Extract data array from response
					const res = response.data || response
					console.log('extracted data:', res)

					// Transform data to add title property for u-picker
					this.columnsCity[0] = res.map(item => ({
						...item,
						title: item.trueName
					}))
					console.log('columnsCity[0] after transform:', this.columnsCity[0])

					if (this.form.type > 1 && res.length > 0) {
						this.$api.service.getCity(res[0].id).then(response1 => {
							const res1 = response1.data || response1
							this.columnsCity[1] = res1.map(item => ({
								...item,
								title: item.trueName
							}))
							if (this.form.type > 2 && res1.length > 0) {
								this.$api.service.getCity(res1[0].id).then(response2 => {
									const res2 = response2.data || response2
									this.columnsCity[2] = res2.map(item => ({
										...item,
										title: item.trueName
									}))
									this.loading = false
								})
							} else {
								this.loading = false
							}
						})
					} else {
						this.loading = false
					}
				}).catch(err => {
					console.error('getcity API error:', err)
					this.loading = false
				})
			},
			confirmCity(Array) {
				this.form.city = Array.value.map((item,index) => {
					if(item == undefined){
						return this.columnsCity[index][0].title
					}else{
						return item.title
					}
				}).join('-')
				this.form.cityId = Array.value.map((e,j) => {
					if(e == undefined){
						return this.columnsCity[j][0].id
					}else{
						return e.id
					}
				})
				this.showCity = false
			},
			confirmType(Array) {
				if (Array.value[0].value == 1) {
					this.columnsCity = [
						[]
					]
				} else if (Array.value[0].value == 2) { //如果用户选择了市级
					this.columnsCity = [
						[],
						[]
					]
				} else if (Array.value[0].value == 3) { //如果用户选择了区县级
					this.columnsCity = [
						[],
						[],
						[]
					]
				}
				this.form.typename = Array.value[0].title
				this.form.type = Array.value[0].value
				this.getcity(0)
				this.show = false
			},
			changeHandler(e) {
				if (this.form.type == 1) return
				const {
					columnIndex,
					index,
					// 微信小程序无法将picker实例传出来，只能通过ref操作
					picker = this.$refs.uPicker
				} = e
				if (columnIndex === 0) {
					this.$api.service.getCity(this.columnsCity[0][index].id).then(response => {
						const res = response.data || response
						const transformedRes = res.map(item => ({
							...item,
							title: item.trueName
						}))
						picker.setColumnValues(1, transformedRes)
						this.columnsCity[1] = transformedRes
						if (this.form.type == 2) return
						if (res.length > 0) {
							this.$api.service.getCity(res[0].id).then(response1 => {
								const res1 = response1.data || response1
								const transformedRes1 = res1.map(item => ({
									...item,
									title: item.trueName
								}))
								picker.setColumnValues(2, transformedRes1)
								this.columnsCity[2] = transformedRes1
							})
						}
					})
				} else if (columnIndex === 1) {
					if (this.form.type == 2) return
					this.$api.service.getCity(this.columnsCity[1][index].id).then(response => {
						const res = response.data || response
						const transformedRes = res.map(item => ({
							...item,
							title: item.trueName
						}))
						picker.setColumnValues(2, transformedRes)
						this.columnsCity[2] = transformedRes
					})
				}
			},
			submit() {
				for (let key in this.form) {
					if (this.form[key] == '') {
						uni.showToast({
							icon: 'none',
							title: '请填写完整提交',
							duration: 1000
						})
						return
					} else if (typeof this.form[key] == 'object' && this.form[key].length == 0) {
						uni.showToast({
							icon: 'none',
							title: '请填写完整提交',
							duration: 1000
						})
						return
					}
				}
				let p = /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;
				if (p.test(this.form.legalPersonIdCard) == false) {
					uni.showToast({
						icon: 'none',
						title: '请填写正确的身份证号'
					})
					return
				}
				let phoneReg = /^1[3456789]\d{9}$/
				if (!phoneReg.test(this.form.legalPersonTel)) {
					uni.showToast({
						icon: 'none',
						title: '请填写正确的手机号',
						duration: 1000
					})
					return
				}
				let subForm = JSON.parse(JSON.stringify(this.form))
				
				subForm.legalPersonIdCardImg1 = subForm.legalPersonIdCardImg1[0].path
				subForm.legalPersonIdCardImg2 = subForm.legalPersonIdCardImg2[0].path
				subForm.legalPersonLicense =subForm.legalPersonLicense[0].path
				delete subForm.city
				delete subForm.typename
				console.log(subForm)
				this.$api.service.dlApply(subForm).then(res => {
					console.log(res)
					if (res) {
						uni.navigateTo({
							url: '/pages/apply_over'
						})
					}
				})

			},
			imgUpload(e) {
				let {
					imagelist,
					imgtype
				} = e;
				this.form[imgtype] = imagelist;
			},
			seeDetails() {
				this.$api.service.dlSee().then(res => {
					if (res) {
						let obj = res
						obj.legalPersonIdCardImg1 = [{
							path: obj.legalPersonIdCardImg1
						}]
						obj.legalPersonIdCardImg2 = [{
							path: obj.legalPersonIdCardImg2
						}]
						obj.legalPersonLicense = [{
							path: obj.legalPersonLicense
						}]
						this.status = obj.status
						for (let key in this.form) {
							this.form[key] = obj[key]
						}
						this.form.typename = this.columns[0][this.form.type-1].title
					}
				}).catch(()=>{
					
				})
			}
		},
		onLoad() {
			this.seeDetails(),
			this.getcity(0)
		},
		
	}
</script>

<style scoped lang="scss">
	.page {
		padding-bottom: 200rpx;

		.header {
			width: 750rpx;
			height: 58rpx;
			background: #FFF7F1;
			line-height: 58rpx;
			text-align: center;
			font-size: 28rpx;
			font-weight: 400;
		}

		.main {
			padding: 40rpx 30rpx;

			.main_item {
				margin-bottom: 20rpx;

				.title {
					margin-bottom: 20rpx;
					font-size: 28rpx;
					font-weight: 400;
					color: #333333;

					span {
						color: #E72427;
					}
				}

				input {
					width: 690rpx;
					height: 110rpx;
					background: #F8F8F8;
					font-size: 28rpx;
					font-weight: 400;
					line-height: 110rpx;
					padding: 0 40rpx;
					box-sizing: border-box;
				}

				.big {
					width: 690rpx;
					height: 388rpx;
					background: #F2FAFE;
					border-radius: 16rpx 16rpx 16rpx 16rpx;

					.top {
						height: 322rpx;
						padding-top: 20rpx;

						.das {
							margin: 0 auto;
							width: 632rpx;
							height: 284rpx;
							border-radius: 0rpx 0rpx 0rpx 0rpx;
							border: 2rpx dashed #2E80FE;
							padding-top: 14rpx;

							.up {
								margin: 0 auto;
								width: 594rpx;
								height: 258rpx;
								background: rgba(0, 0, 0, 0.4);
								border-radius: 12rpx 12rpx 12rpx 12rpx;
							}
						}
					}

					.bottom {
						height: 66rpx;
						width: 690rpx;
						height: 66rpx;
						background: #2E80FE;
						border-radius: 0rpx 0rpx 16rpx 16rpx;
						font-size: 28rpx;
						font-weight: 400;
						color: #FFFFFF;
						line-height: 66rpx;
						text-align: center;
					}
				}

				.card {
					display: flex;
					align-items: center;
					justify-content: space-between;

					.card_item {
						width: 332rpx;
						height: 332rpx;
						background: #F2FAFE;
						border-radius: 16rpx 16rpx 16rpx 16rpx;
						overflow: hidden;

						.top {
							height: 266rpx;
							width: 100%;
							padding-top: 40rpx;

							.das {
								margin: 0 auto;
								width: 266rpx;
								height: 180rpx;
								border: 2rpx dashed #2E80FE;
								padding-top: 28rpx;

								.up {
									margin: 0 auto;
									width: 210rpx;
									height: 130rpx;
								}
							}
						}

						.bottom {
							height: 66rpx;
							width: 100%;
							background-color: #2E80FE;
							font-size: 28rpx;
							font-weight: 400;
							color: #FFFFFF;
							text-align: center;
							line-height: 66rpx;
						}
					}
				}
			}
		}

		.footer {
			padding: 52rpx 30rpx;
			width: 750rpx;
			background: #FFFFFF;
			box-shadow: 0rpx 0rpx 6rpx 2rpx rgba(193, 193, 193, 0.3);
			position: fixed;
			bottom: 0;

			.btn {
				width: 690rpx;
				height: 98rpx;
				background: #2E80FE;
				border-radius: 50rpx 50rpx 50rpx 50rpx;
				font-size: 32rpx;
				font-weight: 500;
				color: #FFFFFF;
				line-height: 98rpx;
				text-align: center;
			}
		}
	}
</style>