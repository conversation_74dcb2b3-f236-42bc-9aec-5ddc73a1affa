{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/user/wait_price.vue?1819", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/user/wait_price.vue?cda5", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/user/wait_price.vue?4fdc", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/user/wait_price.vue?c0cb", "uni-app:///user/wait_price.vue", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/user/wait_price.vue?f361", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/user/wait_price.vue?65f7"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "latitude", "longitude", "id", "currentIndex", "page", "orderList", "status", "showCancel", "methods", "getList", "payType", "pageNum", "pageSize", "item", "uni", "icon", "title", "console", "cancelorder", "content", "confirmText", "cancelText", "success", "fail", "confirmCancel", "delta", "onLoad", "type", "isHighAccuracy", "accuracy", "onBackPress", "url", "onUnload"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,mBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmI;AACnI;AAC8D;AACL;AACsC;;;AAG/F;AACsN;AACtN,gBAAgB,8NAAU;AAC1B,EAAE,gFAAM;AACR,EAAE,iGAAM;AACR,EAAE,0GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,yTAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAAy1B,CAAgB,y2BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCoB72B;EACAC;IACA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;IACA;EACA;;EACAC;IACA;IACAC;MAAA;MACA;MACA;QACAC;QACAC;QACAC;MACA;QACA;QACA;UAAA,uCACAC;YACAH;UAAA;QAAA,CACA;QACA;QACA;QACA;QACA;UACA;QACA;MACA;QACAI;UACAC;UACAC;QACA;QACAC;MACA;IACA;IACA;IACAC;MAAA;MACAJ;QACAE;QACAG;QACAC;QACAC;QACAC;UACA;YACA;UACA;QACA;QACAC;UACAN;QACA;MACA;IACA;IACA;IACAO;MACA;MACA;QACAtB;MACA;QACA;UACAY;YACAC;YACAC;UACA;UACA;UACAF;YACAW;UACA;QACA;MACA;QACAX;UACAC;UACAC;QACA;QACAC;MACA;IACA;EACA;EACAS;IAAA;IACA;IACA;IACA;IACAZ;MACAa;MACAC;MACAC;MACAP;QACA;QACA;MACA;MACAC;QACAT;UACAC;UACAC;QACA;QACAC;MACA;IACA;EACA;EACAa;IACA;IACAhB;MACAiB;IACA;IACA;EACA;EACAC;IACA;IACA;IACA;IACAlB;MACAiB;IACA;EACA;AAEA;AAAA,2B;;;;;;;;;;;;;AC5IA;AAAA;AAAA;AAAA;AAAgmD,CAAgB,ojDAAG,EAAC,C;;;;;;;;;;;ACApnD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "user/wait_price.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/uni-stat/dist/uni-stat.es.js';\nimport Vue from 'vue'\nimport Page from './user/wait_price.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./wait_price.vue?vue&type=template&id=12e45025&scoped=true&\"\nvar renderjs\nimport script from \"./wait_price.vue?vue&type=script&lang=js&\"\nexport * from \"./wait_price.vue?vue&type=script&lang=js&\"\nimport style0 from \"./wait_price.vue?vue&type=style&index=0&id=12e45025&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"12e45025\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"user/wait_price.vue\"\nexport default component.exports", "export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./wait_price.vue?vue&type=template&id=12e45025&scoped=true&\"", "var components\ntry {\n  components = {\n    uCountDown: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-count-down/u-count-down\" */ \"uview-ui/components/u-count-down/u-count-down.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./wait_price.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./wait_price.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"page\">\n\t\t<view class=\"map-container\">\n\t\t\t<map id=\"map\" class=\"map\" :show-location=\"true\" :latitude=\"latitude\" :longitude=\"longitude\"></map>\n\t\t</view>\n\t\t<view class=\"card\">\n\t\t\t<view class=\"title\">已通知多位师傅为您报价</view>\n\t\t\t<view class=\"desc\">师傅可能在服务，请耐心等待</view>\n\t\t\t<view class=\"time\">\n\t\t\t\t距师傅报价截止还剩：\n\t\t\t\t<u-count-down :time=\"24 * 60 * 60 * 1000\" format=\"HH:mm:ss\"></u-count-down>\n\t\t\t</view>\n\t\t</view>\n\t\t<view class=\"footer\">\n\t\t\t<view @click.stop=\"cancelorder()\" class=\"btn\">取消订单</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\nexport default {\n\tdata() {\n\t\treturn {\n\t\t\tlatitude: '', // Latitude for map\n\t\t\tlongitude: '', // Longitude for map\n\t\t\tid: '', // Order ID\n\t\t\tcurrentIndex: 0, // Default payType for getList\n\t\t\tpage: 1, // Default page number\n\t\t\torderList: [], // Store fetched orders\n\t\t\tstatus: 'loadmore', // Load status for pagination\n\t\t\tshowCancel: false // Manage dialog state\n\t\t}\n\t},\n\tmethods: {\n\t\t// Fetch order list\n\t\tgetList(nval) {\n\t\t\tconst payType = nval !== undefined ? nval : this.currentIndex;\n\t\t\treturn this.$api.service.userOrder({\n\t\t\t\tpayType,\n\t\t\t\tpageNum: this.page,\n\t\t\t\tpageSize: 10\n\t\t\t}).then(res => {\n\t\t\t\tconst list = Array.isArray(res.list) ? res.list : [];\n\t\t\t\tconst normalizedList = list.map(item => ({\n\t\t\t\t\t...item,\n\t\t\t\t\tpayType: parseInt(item.payType)\n\t\t\t\t}));\n\t\t\t\tthis.$set(this, 'orderList', normalizedList);\n\t\t\t\tthis.status = list.length < 10 ? 'nomore' : 'loadmore';\n\t\t\t\t// Assign the first order's id\n\t\t\t\tif (normalizedList.length > 0) {\n\t\t\t\t\tthis.id = normalizedList[0].id;\n\t\t\t\t}\n\t\t\t}).catch(err => {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ticon: 'error',\n\t\t\t\t\ttitle: '获取订单失败'\n\t\t\t\t});\n\t\t\t\tconsole.error('API Error:', err);\n\t\t\t});\n\t\t},\n\t\t// Show cancel order confirmation\n\t\tcancelorder() {\n\t\t\tuni.showModal({\n\t\t\t\ttitle: '提示',\n\t\t\t\tcontent: '确定要取消订单吗？',\n\t\t\t\tconfirmText: '确定',\n\t\t\t\tcancelText: '取消',\n\t\t\t\tsuccess: (res) => {\n\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\tthis.confirmCancel();\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\tfail: (err) => {\n\t\t\t\t\tconsole.error('Modal Error:', err);\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\t// Confirm order cancellation\n\t\tconfirmCancel() {\n\t\t\tthis.showCancel = false;\n\t\t\tthis.$api.service.cancelOrder({\n\t\t\t\tid: this.id\n\t\t\t}).then(res => {\n\t\t\t\tif (res.code === \"200\") {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: 'success',\n\t\t\t\t\t\ttitle: '取消成功'\n\t\t\t\t\t});\n\t\t\t\t\t// Navigate back to order list after cancellation\n\t\t\t\t\tuni.navigateBack({\n\t\t\t\t\t\tdelta: 9\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t}).catch(err => {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ticon: 'error',\n\t\t\t\t\ttitle: '取消失败'\n\t\t\t\t});\n\t\t\t\tconsole.error('Cancel Error:', err);\n\t\t\t});\n\t\t}\n\t},\n\tonLoad(options) {\n\t\t// Fetch order list\n\t\tthis.getList();\n\t\t// Get user location\n\t\tuni.getLocation({\n\t\t\ttype: 'gcj02',\n\t\t\tisHighAccuracy: true,\n\t\t\taccuracy: 'best',\n\t\t\tsuccess: (res) => {\n\t\t\t\tthis.latitude = res.latitude;\n\t\t\t\tthis.longitude = res.longitude;\n\t\t\t},\n\t\t\tfail: (err) => {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ticon: 'error',\n\t\t\t\t\ttitle: '获取位置失败'\n\t\t\t\t});\n\t\t\t\tconsole.error('Get Location Error:', err);\n\t\t\t}\n\t\t});\n\t},\n\tonBackPress() {\n\t\t// Override back button to navigate to order_list\n\t\tuni.redirectTo({\n\t\t\turl: '/user/order_list'\n\t\t});\n\t\treturn true; // Prevent default back behavior\n\t},\n\tonUnload() {\n\t\t// uni.navigateBack({\n\t\t// \tdelta:9\n\t\t// })\n\t\tuni.reLaunch({\n\t\t\turl: '/user/tiaozhuan'\n\t\t});\n\t},\n\n}\n</script>\n\n<style scoped lang=\"scss\">\n.page {\n\twidth: 750rpx;\n\theight: 100vh;\n\tposition: relative;\n\n\t.map-container {\n\t\twidth: 100%;\n\t\theight: calc(100vh - 192rpx);\n\t\tposition: relative;\n\t}\n\n\t.map {\n\t\twidth: 100%;\n\t\theight: 100%;\n\t}\n\n\t.card {\n\t\twidth: 686rpx;\n\t\theight: 234rpx;\n\t\tbackground: #FFFFFF;\n\t\tborder-radius: 16rpx;\n\t\tposition: absolute;\n\t\tleft: 32rpx;\n\t\ttop: 40rpx;\n\t\tpadding: 34rpx;\n\n\t\t.title {\n\t\t\tfont-size: 40rpx;\n\t\t\tfont-weight: 500;\n\t\t\tcolor: #333333;\n\t\t}\n\n\t\t.desc {\n\t\t\tmargin-top: 20rpx;\n\t\t\tfont-size: 24rpx;\n\t\t\tfont-weight: 400;\n\t\t\tcolor: #ADADAD;\n\t\t}\n\n\t\t.time {\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\tmargin-top: 20rpx;\n\t\t\tfont-size: 24rpx;\n\t\t\tfont-weight: 400;\n\t\t\tcolor: #ADADAD;\n\n\t\t\t::v-deep .u-count-down__text {\n\t\t\t\tcolor: #E72427;\n\t\t\t}\n\t\t}\n\t}\n\n\t.footer {\n\t\twidth: 100%;\n\t\theight: 192rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tposition: fixed;\n\t\tbottom: 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t\tbackground-color: #FFFFFF;\n\t\tz-index: 9999;\n\t\tbox-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);\n\t\t/* #ifdef APP-PLUS */\n\t\tpadding-bottom: env(safe-area-inset-bottom);\n\t\t/* #endif */\n\n\t\t.btn {\n\t\t\twidth: 686rpx;\n\t\t\theight: 88rpx;\n\t\t\tbackground: #2E80FE;\n\t\t\tborder-radius: 44rpx;\n\t\t\tfont-size: 28rpx;\n\t\t\tfont-weight: 400;\n\t\t\tcolor: #FFFFFF;\n\t\t\tline-height: 88rpx;\n\t\t\ttext-align: center;\n\t\t\tbox-shadow: 0 4rpx 12rpx rgba(46, 128, 254, 0.3);\n\t\t}\n\t}\n}\n</style>", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./wait_price.vue?vue&type=style&index=0&id=12e45025&scoped=true&lang=scss&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./wait_price.vue?vue&type=style&index=0&id=12e45025&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755335917227\n      var cssReload = require(\"E:/BaiduNetdiskDownload/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}