{"version": 3, "sources": ["webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/node_modules/uview-ui/components/u-loading-icon/u-loading-icon.vue?5920", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/node_modules/uview-ui/components/u-loading-icon/u-loading-icon.vue?a6ea", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/node_modules/uview-ui/components/u-loading-icon/u-loading-icon.vue?3611", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/node_modules/uview-ui/components/u-loading-icon/u-loading-icon.vue?ec0e", "uni-app:///node_modules/uview-ui/components/u-loading-icon/u-loading-icon.vue", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/node_modules/uview-ui/components/u-loading-icon/u-loading-icon.vue?2078", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/node_modules/uview-ui/components/u-loading-icon/u-loading-icon.vue?c7d1"], "names": ["name", "mixins", "data", "array12", "length", "aniAngel", "webviewHide", "loading", "computed", "otherBorderColor", "watch", "show", "mounted", "methods", "init", "setTimeout", "addEventListenerToWebview", "currentWebview"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAuI;AACvI;AACkE;AACL;AACsC;;;AAGnG;AACsN;AACtN,gBAAgB,8NAAU;AAC1B,EAAE,oFAAM;AACR,EAAE,qGAAM;AACR,EAAE,8GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,yGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACvBA;AAAA;AAAA;AAAA;AAA61B,CAAgB,62BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;AC4Dj3B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAjBA,eAkBA;EACAA;EACAC;EACAC;IACA;MACA;MACA;MACAC;QACAC;MACA;MACA;MACA;MACAC;MAAA;MACAC;MAAA;MACAC;IACA;EACA;;EACAC;IACA;IACA;IACA;IACAC;MACA;MACA;QACA;MACA;QACA;MACA;MACA;IACA;EACA;;EACAC;IACAC;MACA;IAAA;EASA;EACAC;IACA;EACA;EACAC;IACAC;MACAC,wBAOA;IACA;IACA;IACAC;MAAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACAC;QACA;MACA;MACAA;QACA;MACA;IACA;EA8BA;AACA;AAAA,2B;;;;;;;;;;;;;ACzLA;AAAA;AAAA;AAAA;AAAomD,CAAgB,wjDAAG,EAAC,C;;;;;;;;;;;ACAxnD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "node-modules/uview-ui/components/u-loading-icon/u-loading-icon.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-loading-icon.vue?vue&type=template&id=8ae91632&scoped=true&\"\nvar renderjs\nimport script from \"./u-loading-icon.vue?vue&type=script&lang=js&\"\nexport * from \"./u-loading-icon.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-loading-icon.vue?vue&type=style&index=0&id=8ae91632&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"8ae91632\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"node_modules/uview-ui/components/u-loading-icon/u-loading-icon.vue\"\nexport default component.exports", "export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-loading-icon.vue?vue&type=template&id=8ae91632&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var s0 = _vm.show ? _vm.__get_style([_vm.$u.addStyle(_vm.customStyle)]) : null\n  var g0 = _vm.show && !_vm.webviewHide ? _vm.$u.addUnit(_vm.size) : null\n  var g1 = _vm.show && !_vm.webviewHide ? _vm.$u.addUnit(_vm.size) : null\n  var g2 = _vm.show && _vm.text ? _vm.$u.addUnit(_vm.textSize) : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        s0: s0,\n        g0: g0,\n        g1: g1,\n        g2: g2,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-loading-icon.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-loading-icon.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view\r\n\t\tclass=\"u-loading-icon\"\r\n\t\t:style=\"[$u.addStyle(customStyle)]\"\r\n\t\t:class=\"[vertical && 'u-loading-icon--vertical']\"\r\n\t\tv-if=\"show\"\r\n\t>\r\n\t\t<view\r\n\t\t\tv-if=\"!webviewHide\"\r\n\t\t\tclass=\"u-loading-icon__spinner\"\r\n\t\t\t:class=\"[`u-loading-icon__spinner--${mode}`]\"\r\n\t\t\tref=\"ani\"\r\n\t\t\t:style=\"{\r\n\t\t\t\tcolor: color,\r\n\t\t\t\twidth: $u.addUnit(size),\r\n\t\t\t\theight: $u.addUnit(size),\r\n\t\t\t\tborderTopColor: color,\r\n\t\t\t\tborderBottomColor: otherBorderColor,\r\n\t\t\t\tborderLeftColor: otherBorderColor,\r\n\t\t\t\tborderRightColor: otherBorderColor,\r\n\t\t\t\t'animation-duration': `${duration}ms`,\r\n\t\t\t\t'animation-timing-function': mode === 'semicircle' || mode === 'circle' ? timingFunction : ''\r\n\t\t\t}\"\r\n\t\t>\r\n\t\t\t<block v-if=\"mode === 'spinner'\">\r\n\t\t\t\t<!-- #ifndef APP-NVUE -->\r\n\t\t\t\t<view\r\n\t\t\t\t\tv-for=\"(item, index) in array12\"\r\n\t\t\t\t\t:key=\"index\"\r\n\t\t\t\t\tclass=\"u-loading-icon__dot\"\r\n\t\t\t\t>\r\n\t\t\t\t</view>\r\n\t\t\t\t<!-- #endif -->\r\n\t\t\t\t<!-- #ifdef APP-NVUE -->\r\n\t\t\t\t<!-- 此组件内部图标部分无法设置宽高，即使通过width和height配置了也无效 -->\r\n\t\t\t\t<loading-indicator\r\n\t\t\t\t\tv-if=\"!webviewHide\"\r\n\t\t\t\t\tclass=\"u-loading-indicator\"\r\n\t\t\t\t\t:animating=\"true\"\r\n\t\t\t\t\t:style=\"{\r\n\t\t\t\t\t\tcolor: color,\r\n\t\t\t\t\t\twidth: $u.addUnit(size),\r\n\t\t\t\t\t\theight: $u.addUnit(size)\r\n\t\t\t\t\t}\"\r\n\t\t\t\t/>\r\n\t\t\t\t<!-- #endif -->\r\n\t\t\t</block>\r\n\t\t</view>\r\n\t\t<text\r\n\t\t\tv-if=\"text\"\r\n\t\t\tclass=\"u-loading-icon__text\"\r\n\t\t\t:style=\"{\r\n\t\t\t\tfontSize: $u.addUnit(textSize),\r\n\t\t\t\tcolor: textColor,\r\n\t\t\t}\"\r\n\t\t>{{text}}</text>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport props from './props.js';\r\n\t// #ifdef APP-NVUE\r\n\tconst animation = weex.requireModule('animation');\r\n\t// #endif\r\n\t/**\r\n\t * loading 加载动画\r\n\t * @description 警此组件为一个小动画，目前用在uView的loadmore加载更多和switch开关等组件的正在加载状态场景。\r\n\t * @tutorial https://www.uviewui.com/components/loading.html\r\n\t * @property {Boolean}\t\t\tshow\t\t\t是否显示组件  (默认 true)\r\n\t * @property {String}\t\t\tcolor\t\t\t动画活动区域的颜色，只对 mode = flower 模式有效（默认color['u-tips-color']）\r\n\t * @property {String}\t\t\ttextColor\t\t提示文本的颜色（默认color['u-tips-color']）\r\n\t * @property {Boolean}\t\t\tvertical\t\t文字和图标是否垂直排列 (默认 false )\r\n\t * @property {String}\t\t\tmode\t\t\t模式选择，见官网说明（默认 'circle' ）\r\n\t * @property {String | Number}\tsize\t\t\t加载图标的大小，单位px （默认 24 ）\r\n\t * @property {String | Number}\ttextSize\t\t文字大小（默认 15 ）\r\n\t * @property {String | Number}\ttext\t\t\t文字内容 \r\n\t * @property {String}\t\t\ttimingFunction\t动画模式 （默认 'ease-in-out' ）\r\n\t * @property {String | Number}\tduration\t\t动画执行周期时间（默认 1200）\r\n\t * @property {String}\t\t\tinactiveColor\tmode=circle时的暗边颜色 \r\n\t * @property {Object}\t\t\tcustomStyle\t\t定义需要用到的外部样式\r\n\t * @example <u-loading mode=\"circle\"></u-loading>\r\n\t */\r\n\texport default {\r\n\t\tname: 'u-loading-icon',\r\n\t\tmixins: [uni.$u.mpMixin, uni.$u.mixin, props],\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\t// Array.form可以通过一个伪数组对象创建指定长度的数组\r\n\t\t\t\t// https://developer.mozilla.org/zh-CN/docs/Web/JavaScript/Reference/Global_Objects/Array/from\r\n\t\t\t\tarray12: Array.from({\r\n\t\t\t\t\tlength: 12\r\n\t\t\t\t}),\r\n\t\t\t\t// 这里需要设置默认值为360，否则在安卓nvue上，会延迟一个duration周期后才执行\r\n\t\t\t\t// 在iOS nvue上，则会一开始默认执行两个周期的动画\r\n\t\t\t\taniAngel: 360, // 动画旋转角度\r\n\t\t\t\twebviewHide: false, // 监听webview的状态，如果隐藏了页面，则停止动画，以免性能消耗\r\n\t\t\t\tloading: false, // 是否运行中，针对nvue使用\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\t// 当为circle类型时，给其另外三边设置一个更轻一些的颜色\r\n\t\t\t// 之所以需要这么做的原因是，比如父组件传了color为红色，那么需要另外的三个边为浅红色\r\n\t\t\t// 而不能是固定的某一个其他颜色(因为这个固定的颜色可能浅蓝，导致效果没有那么细腻良好)\r\n\t\t\totherBorderColor() {\r\n\t\t\t\tconst lightColor = uni.$u.colorGradient(this.color, '#ffffff', 100)[80]\r\n\t\t\t\tif (this.mode === 'circle') {\r\n\t\t\t\t\treturn this.inactiveColor ? this.inactiveColor : lightColor\r\n\t\t\t\t} else {\r\n\t\t\t\t\treturn 'transparent'\r\n\t\t\t\t}\r\n\t\t\t\t// return this.mode === 'circle' ? this.inactiveColor ? this.inactiveColor : lightColor : 'transparent'\r\n\t\t\t}\r\n\t\t},\r\n\t\twatch: {\r\n\t\t\tshow(n) {\r\n\t\t\t\t// nvue中，show为true，且为非loading状态，就重新执行动画模块\r\n\t\t\t\t// #ifdef APP-NVUE\r\n\t\t\t\tif (n && !this.loading) {\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\tthis.startAnimate()\r\n\t\t\t\t\t}, 30)\r\n\t\t\t\t}\r\n\t\t\t\t// #endif\r\n\t\t\t}\r\n\t\t},\r\n\t\tmounted() {\r\n\t\t\tthis.init()\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tinit() {\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t// #ifdef APP-NVUE\r\n\t\t\t\t\tthis.show && this.nvueAnimate()\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t\t// #ifdef APP-PLUS \r\n\t\t\t\t\tthis.show && this.addEventListenerToWebview()\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t}, 20)\r\n\t\t\t},\r\n\t\t\t// 监听webview的显示与隐藏\r\n\t\t\taddEventListenerToWebview() {\r\n\t\t\t\t// webview的堆栈\r\n\t\t\t\tconst pages = getCurrentPages()\r\n\t\t\t\t// 当前页面\r\n\t\t\t\tconst page = pages[pages.length - 1]\r\n\t\t\t\t// 当前页面的webview实例\r\n\t\t\t\tconst currentWebview = page.$getAppWebview()\r\n\t\t\t\t// 监听webview的显示与隐藏，从而停止或者开始动画(为了性能)\r\n\t\t\t\tcurrentWebview.addEventListener('hide', () => {\r\n\t\t\t\t\tthis.webviewHide = true\r\n\t\t\t\t})\r\n\t\t\t\tcurrentWebview.addEventListener('show', () => {\r\n\t\t\t\t\tthis.webviewHide = false\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// #ifdef APP-NVUE\r\n\t\t\tnvueAnimate() {\r\n\t\t\t\t// nvue下，非spinner类型时才需要旋转，因为nvue的spinner类型，使用了weex的\r\n\t\t\t\t// loading-indicator组件，自带旋转功能\r\n\t\t\t\tthis.mode !== 'spinner' && this.startAnimate()\r\n\t\t\t},\r\n\t\t\t// 执行nvue的animate模块动画\r\n\t\t\tstartAnimate() {\r\n\t\t\t\tthis.loading = true\r\n\t\t\t\tconst ani = this.$refs.ani\r\n\t\t\t\tif (!ani) return\r\n\t\t\t\tanimation.transition(ani, {\r\n\t\t\t\t\t// 进行角度旋转\r\n\t\t\t\t\tstyles: {\r\n\t\t\t\t\t\ttransform: `rotate(${this.aniAngel}deg)`,\r\n\t\t\t\t\t\ttransformOrigin: 'center center'\r\n\t\t\t\t\t},\r\n\t\t\t\t\tduration: this.duration,\r\n\t\t\t\t\ttimingFunction: this.timingFunction,\r\n\t\t\t\t\t// delay: 10\r\n\t\t\t\t}, () => {\r\n\t\t\t\t\t// 每次增加360deg，为了让其重新旋转一周\r\n\t\t\t\t\tthis.aniAngel += 360\r\n\t\t\t\t\t// 动画结束后，继续循环执行动画，需要同时判断webviewHide变量\r\n\t\t\t\t\t// nvue安卓，页面隐藏后依然会继续执行startAnimate方法\r\n\t\t\t\t\tthis.show && !this.webviewHide ? this.startAnimate() : this.loading = false\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t\t// #endif\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t@import \"../../libs/css/components.scss\";\r\n\t$u-loading-icon-color: #c8c9cc !default;\r\n\t$u-loading-icon-text-margin-left:4px !default;\r\n\t$u-loading-icon-text-color:$u-content-color !default;\r\n\t$u-loading-icon-text-font-size:14px !default;\r\n\t$u-loading-icon-text-line-height:20px !default;\r\n\t$u-loading-width:30px !default;\r\n\t$u-loading-height:30px !default;\r\n\t$u-loading-max-width:100% !default;\r\n\t$u-loading-max-height:100% !default;\r\n\t$u-loading-semicircle-border-width: 2px !default;\r\n\t$u-loading-semicircle-border-color:transparent !default;\r\n\t$u-loading-semicircle-border-top-right-radius: 100px !default;\r\n\t$u-loading-semicircle-border-top-left-radius: 100px !default;\r\n\t$u-loading-semicircle-border-bottom-left-radius: 100px !default;\r\n\t$u-loading-semicircle-border-bottom-right-radiu: 100px !default;\r\n\t$u-loading-semicircle-border-style: solid !default;\r\n\t$u-loading-circle-border-top-right-radius: 100px !default;\r\n\t$u-loading-circle-border-top-left-radius: 100px !default;\r\n\t$u-loading-circle-border-bottom-left-radius: 100px !default;\r\n\t$u-loading-circle-border-bottom-right-radiu: 100px !default;\r\n\t$u-loading-circle-border-width:2px !default;\r\n\t$u-loading-circle-border-top-color:#e5e5e5 !default;\r\n\t$u-loading-circle-border-right-color:$u-loading-circle-border-top-color !default;\r\n\t$u-loading-circle-border-bottom-color:$u-loading-circle-border-top-color !default;\r\n\t$u-loading-circle-border-left-color:$u-loading-circle-border-top-color !default;\r\n\t$u-loading-circle-border-style:solid !default;\r\n\t$u-loading-icon-host-font-size:0px !default;\r\n\t$u-loading-icon-host-line-height:1 !default;\r\n\t$u-loading-icon-vertical-margin:6px 0 0 !default;\r\n\t$u-loading-icon-dot-top:0 !default;\r\n\t$u-loading-icon-dot-left:0 !default;\r\n\t$u-loading-icon-dot-width:100% !default;\r\n\t$u-loading-icon-dot-height:100% !default;\r\n\t$u-loading-icon-dot-before-width:2px !default;\r\n\t$u-loading-icon-dot-before-height:25% !default;\r\n\t$u-loading-icon-dot-before-margin:0 auto !default;\r\n\t$u-loading-icon-dot-before-background-color:currentColor !default;\r\n\t$u-loading-icon-dot-before-border-radius:40% !default;\r\n\r\n\t.u-loading-icon {\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\t// display: inline-flex;\r\n\t\t/* #endif */\r\n\t\tflex-direction: row;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tcolor: $u-loading-icon-color;\r\n\r\n\t\t&__text {\r\n\t\t\tmargin-left: $u-loading-icon-text-margin-left;\r\n\t\t\tcolor: $u-loading-icon-text-color;\r\n\t\t\tfont-size: $u-loading-icon-text-font-size;\r\n\t\t\tline-height: $u-loading-icon-text-line-height;\r\n\t\t}\r\n\r\n\t\t&__spinner {\r\n\t\t\twidth: $u-loading-width;\r\n\t\t\theight: $u-loading-height;\r\n\t\t\tposition: relative;\r\n\t\t\t/* #ifndef APP-NVUE */\r\n\t\t\tbox-sizing: border-box;\r\n\t\t\tmax-width: $u-loading-max-width;\r\n\t\t\tmax-height: $u-loading-max-height;\r\n\t\t\tanimation: u-rotate 1s linear infinite;\r\n\t\t\t/* #endif */\r\n\t\t}\r\n\r\n\t\t&__spinner--semicircle {\r\n\t\t\tborder-width: $u-loading-semicircle-border-width;\r\n\t\t\tborder-color: $u-loading-semicircle-border-color;\r\n\t\t\tborder-top-right-radius: $u-loading-semicircle-border-top-right-radius;\r\n\t\t\tborder-top-left-radius: $u-loading-semicircle-border-top-left-radius;\r\n\t\t\tborder-bottom-left-radius: $u-loading-semicircle-border-bottom-left-radius;\r\n\t\t\tborder-bottom-right-radius: $u-loading-semicircle-border-bottom-right-radiu;\r\n\t\t\tborder-style: $u-loading-semicircle-border-style;\r\n\t\t}\r\n\r\n\t\t&__spinner--circle {\r\n\t\t\tborder-top-right-radius: $u-loading-circle-border-top-right-radius;\r\n\t\t\tborder-top-left-radius: $u-loading-circle-border-top-left-radius;\r\n\t\t\tborder-bottom-left-radius: $u-loading-circle-border-bottom-left-radius;\r\n\t\t\tborder-bottom-right-radius: $u-loading-circle-border-bottom-right-radiu;\r\n\t\t\tborder-width: $u-loading-circle-border-width;\r\n\t\t\tborder-top-color: $u-loading-circle-border-top-color;\r\n\t\t\tborder-right-color: $u-loading-circle-border-right-color;\r\n\t\t\tborder-bottom-color: $u-loading-circle-border-bottom-color;\r\n\t\t\tborder-left-color: $u-loading-circle-border-left-color;\r\n\t\t\tborder-style: $u-loading-circle-border-style;\r\n\t\t}\r\n\r\n\t\t&--vertical {\r\n\t\t\tflex-direction: column\r\n\t\t}\r\n\t}\r\n\r\n\t/* #ifndef APP-NVUE */\r\n\t:host {\r\n\t\tfont-size: $u-loading-icon-host-font-size;\r\n\t\tline-height: $u-loading-icon-host-line-height;\r\n\t}\r\n\r\n\t.u-loading-icon {\r\n\t\t&__spinner--spinner {\r\n\t\t\tanimation-timing-function: steps(12)\r\n\t\t}\r\n\r\n\t\t&__text:empty {\r\n\t\t\tdisplay: none\r\n\t\t}\r\n\r\n\t\t&--vertical &__text {\r\n\t\t\tmargin: $u-loading-icon-vertical-margin;\r\n\t\t\tcolor: $u-content-color;\r\n\t\t}\r\n\r\n\t\t&__dot {\r\n\t\t\tposition: absolute;\r\n\t\t\ttop: $u-loading-icon-dot-top;\r\n\t\t\tleft: $u-loading-icon-dot-left;\r\n\t\t\twidth: $u-loading-icon-dot-width;\r\n\t\t\theight: $u-loading-icon-dot-height;\r\n\r\n\t\t\t&:before {\r\n\t\t\t\tdisplay: block;\r\n\t\t\t\twidth: $u-loading-icon-dot-before-width;\r\n\t\t\t\theight: $u-loading-icon-dot-before-height;\r\n\t\t\t\tmargin: $u-loading-icon-dot-before-margin;\r\n\t\t\t\tbackground-color: $u-loading-icon-dot-before-background-color;\r\n\t\t\t\tborder-radius: $u-loading-icon-dot-before-border-radius;\r\n\t\t\t\tcontent: \" \"\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t@for $i from 1 through 12 {\r\n\t\t.u-loading-icon__dot:nth-of-type(#{$i}) {\r\n\t\t\ttransform: rotate($i * 30deg);\r\n\t\t\topacity: 1 - 0.0625 * ($i - 1);\r\n\t\t}\r\n\t}\r\n\r\n\t@keyframes u-rotate {\r\n\t\t0% {\r\n\t\t\ttransform: rotate(0deg)\r\n\t\t}\r\n\r\n\t\tto {\r\n\t\t\ttransform: rotate(1turn)\r\n\t\t}\r\n\t}\r\n\r\n\t/* #endif */\r\n</style>\r\n", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-loading-icon.vue?vue&type=style&index=0&id=8ae91632&lang=scss&scoped=true&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-loading-icon.vue?vue&type=style&index=0&id=8ae91632&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755337031912\n      var cssReload = require(\"E:/BaiduNetdiskDownload/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}