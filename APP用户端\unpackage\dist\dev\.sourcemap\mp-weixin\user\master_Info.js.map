{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/user/master_Info.vue?3f8f", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/user/master_Info.vue?ff5b", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/user/master_Info.vue?8abd", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/user/master_Info.vue?0cee", "uni-app:///user/master_Info.vue", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/user/master_Info.vue?6c31", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/user/master_Info.vue?c8e2"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "showCity", "loading", "columnsCity", "form", "id", "coach_name", "mobile", "sex", "work_time", "address", "lng", "lat", "text", "city", "city_id", "id_code", "id_card1", "id_card2", "self_img", "methods", "getcity", "<PERSON><PERSON><PERSON><PERSON>", "columnIndex", "e", "index", "picker", "confirmCity", "submit", "console", "uni", "icon", "title", "duration", "obj", "setTimeout", "imgUpload", "imagelist", "imgtype", "getInfo", "path", "onLoad"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,oBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoI;AACpI;AAC+D;AACL;AACsC;;;AAGhG;AACsN;AACtN,gBAAgB,8NAAU;AAC1B,EAAE,iFAAM;AACR,EAAE,kGAAM;AACR,EAAE,2GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,aAAa,+TAEN;AACP,KAAK;AACL;AACA,aAAa,2RAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACnDA;AAAA;AAAA;AAAA;AAA01B,CAAgB,02BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCgF92B;EACAC;IACA;MACAC;MACAC;MACAC,cACA,IACA,IACA,GACA;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;IACA;EACA;EACAC;IACAC;MAAA;MACA;QACA;QACA;UACA;UACA;YACA;UACA;QACA;MACA;IACA;IACAC;MAAA;MACA,IACAC,cAIAC,EAJAD;QACAE,QAGAD,EAHAC;QAAA,YAGAD,EADAE;QAAAA;MAEA;QACA;UACAA;UACA;UACA;YACAA;YACA;UACA;QACA;MACA;QACA;UACAA;UACA;QACA;MACA;IACA;IACAC;MAAA;MACA;QACA;UACA;QACA;UACA;QACA;MACA;MACA;QACA;UACA;QACA;UACA;QACA;MACA;MACA;IACA;IACAC;MAAA;MACAC;MACA;QACA;UACAC;YACAC;YACAC;UACA;UACA;QAEA;UACAF;YACAC;YACAC;UACA;UACA;QACA;MACA;MACA;MACA;QACAF;UACAC;UACAC;QACA;QACA;MACA;MACA;MACA;QACAF;UACAC;UACAC;UACAC;QACA;QACA;MACA;MACA;MACAC;MACAA;MACAA;QAAA;MAAA;MACA;MACA;MACA;QACAJ;UACAC;UACAC;QACA;QACAG;UACAL;QACA;MACA;IACA;IACAM;MACA,IACAC,YAEAb,EAFAa;QACAC,UACAd,EADAc;MAEA;IACA;IACAC;MAAA;MACA;QACA;QACAL;UACAM;QACA;QACAN;UACAM;QACA;QACAN;UACAM;QACA;QACA;UACA;QACA;QACA;MACA;IACA;EACA;EACAC;IACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AClPA;AAAA;AAAA;AAAA;AAAimD,CAAgB,qjDAAG,EAAC,C;;;;;;;;;;;ACArnD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "user/master_Info.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/uni-stat/dist/uni-stat.es.js';\nimport Vue from 'vue'\nimport Page from './user/master_Info.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./master_Info.vue?vue&type=template&id=68189f56&scoped=true&\"\nvar renderjs\nimport script from \"./master_Info.vue?vue&type=script&lang=js&\"\nexport * from \"./master_Info.vue?vue&type=script&lang=js&\"\nimport style0 from \"./master_Info.vue?vue&type=style&index=0&id=68189f56&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"68189f56\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"user/master_Info.vue\"\nexport default component.exports", "export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./master_Info.vue?vue&type=template&id=68189f56&scoped=true&\"", "var components\ntry {\n  components = {\n    uPicker: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-picker/u-picker\" */ \"uview-ui/components/u-picker/u-picker.vue\"\n      )\n    },\n    uRadioGroup: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-radio-group/u-radio-group\" */ \"uview-ui/components/u-radio-group/u-radio-group.vue\"\n      )\n    },\n    uRadio: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-radio/u-radio\" */ \"uview-ui/components/u-radio/u-radio.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.showCity = false\n    }\n    _vm.e1 = function ($event) {\n      _vm.showCity = true\n    }\n  }\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./master_Info.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./master_Info.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"page\">\r\n\t\t<view class=\"main\">\r\n\t\t\t<u-picker :show=\"showCity\" ref=\"uPicker\" :loading=\"loading\" :columns=\"columnsCity\" @change=\"changeHandler\"\r\n\t\t\t\tkeyName=\"title\" @cancel=\"showCity = false\" @confirm=\"confirmCity\"></u-picker>\r\n\t\t\t<view class=\"main_item\">\r\n\t\t\t\t<view class=\"title\"><span>*</span>姓名</view>\r\n\t\t\t\t<input type=\"text\" v-model=\"form.coach_name\" placeholder=\"请输入姓名\">\r\n\t\t\t</view>\r\n\t\t\t<view class=\"main_item\">\r\n\t\t\t\t<view class=\"title\"><span>*</span>手机号</view>\r\n\t\t\t\t<input type=\"text\" v-model=\"form.mobile\" placeholder=\"请输入手机号\">\r\n\t\t\t</view>\r\n\t\t\t<view class=\"main_item\">\r\n\t\t\t\t<view class=\"title\"><span>*</span>性别</view>\r\n\t\t\t\t<u-radio-group v-model=\"form.sex\" placement=\"row\">\r\n\t\t\t\t\t<u-radio :customStyle=\"{marginRight: '20px'}\" label=\"男\" :name=\"0\"></u-radio>\r\n\t\t\t\t\t<u-radio label=\"女\" :name=\"1\"></u-radio>\r\n\t\t\t\t</u-radio-group>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"main_item\">\r\n\t\t\t\t<view class=\"title\"><span>*</span>从业年份</view>\r\n\t\t\t\t<input type=\"text\" v-model=\"form.work_time\" placeholder=\"请输入从业年份\">\r\n\t\t\t</view>\r\n\t\t\t<view class=\"main_item\">\r\n\t\t\t\t<view class=\"title\"><span>*</span>所在地址</view>\r\n\t\t\t\t<input type=\"text\" v-model=\"form.address\" placeholder=\"请输入所在地址\">\r\n\t\t\t</view>\r\n\t\t\t<view class=\"main_item\">\r\n\t\t\t\t<view class=\"title\"><span>*</span>选择区域</view>\r\n\t\t\t\t<input type=\"text\" v-model=\"form.city\" placeholder=\"请选择代理区域\" disabled @click=\"showCity = true\">\r\n\t\t\t</view>\r\n\t\t\t<view class=\"main_item\">\r\n\t\t\t\t<view class=\"title\"><span>*</span>自我介绍</view>\r\n\t\t\t\t<input type=\"text\" v-model=\"form.text\" placeholder=\"请输入自我介绍\">\r\n\t\t\t</view>\r\n\t\t\t<view class=\"main_item\">\r\n\t\t\t\t<view class=\"title\"><span>*</span>身份证号</view>\r\n\t\t\t\t<input type=\"text\" v-model=\"form.id_code\" placeholder=\"请输入身份证号\">\r\n\t\t\t</view>\r\n\t\t\t<view class=\"main_item\">\r\n\t\t\t\t<view class=\"title\"><span>*</span>上传身份证照片</view>\r\n\t\t\t\t<view class=\"card\">\r\n\t\t\t\t\t<view class=\"card_item\">\r\n\t\t\t\t\t\t<view class=\"top\">\r\n\t\t\t\t\t\t\t<view class=\"das\">\r\n\t\t\t\t\t\t\t\t<view class=\"up\">\r\n\t\t\t\t\t\t\t\t\t<upload @upload=\"imgUpload\" :imagelist=\"form.id_card1\" imgtype=\"id_card1\"\r\n\t\t\t\t\t\t\t\t\t\timgclass=\"id_card_box\" text=\"身份证人像面\" :imgsize=\"1\"></upload>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"bottom\">拍摄人像面</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"card_item\">\r\n\t\t\t\t\t\t<view class=\"top\">\r\n\t\t\t\t\t\t\t<view class=\"das\">\r\n\t\t\t\t\t\t\t\t<view class=\"up\">\r\n\t\t\t\t\t\t\t\t\t<upload @upload=\"imgUpload\" :imagelist=\"form.id_card2\" imgtype=\"id_card2\"\r\n\t\t\t\t\t\t\t\t\t\timgclass=\"id_card_box\" text=\"身份证国徽面\" :imgsize=\"1\"></upload>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"bottom\">拍摄国徽面</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"main_item\">\r\n\t\t\t\t<view class=\"title\"><span>*</span>上传形象照片</view>\r\n\t\t\t\t<upload @upload=\"imgUpload\" @del=\"imgUpload\" :imagelist=\"form.self_img\" imgtype=\"self_img\" imgclass=\"\"\r\n\t\t\t\t\ttext=\"形象照片\" :imgsize=\"3\"></upload>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"footer\">\r\n\t\t\t<view class=\"btn\" @click=\"submit\">保存</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tshowCity: false,\r\n\t\t\t\tloading: false,\r\n\t\t\t\tcolumnsCity: [\r\n\t\t\t\t\t[],\r\n\t\t\t\t\t[],\r\n\t\t\t\t\t[]\r\n\t\t\t\t],\r\n\t\t\t\tform: {\r\n\t\t\t\t\tid:'',\r\n\t\t\t\t\tcoach_name: '',\r\n\t\t\t\t\tmobile: '',\r\n\t\t\t\t\tsex: 0,\r\n\t\t\t\t\twork_time: '',\r\n\t\t\t\t\taddress: '',\r\n\t\t\t\t\tlng:'',\r\n\t\t\t\t\tlat:'',\r\n\t\t\t\t\ttext: '',\r\n\t\t\t\t\tcity:'',\r\n\t\t\t\t\tcity_id:'',\r\n\t\t\t\t\tid_code: '',\r\n\t\t\t\t\tid_card1: [],\r\n\t\t\t\t\tid_card2: [],\r\n\t\t\t\t\tself_img: []\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tgetcity(e) {\r\n\t\t\t\tthis.$api.service.getCity(e).then(res => {\r\n\t\t\t\t\tthis.columnsCity[0] = res\r\n\t\t\t\t\tthis.$api.service.getCity(res[0].id).then(res1 => {\r\n\t\t\t\t\t\tthis.columnsCity[1] = res1\r\n\t\t\t\t\t\tthis.$api.service.getCity(res1[0].id).then(res2 => {\r\n\t\t\t\t\t\t\tthis.columnsCity[2] = res2\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t})\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tchangeHandler(e) {\r\n\t\t\t\tconst {\r\n\t\t\t\t\tcolumnIndex,\r\n\t\t\t\t\tindex,\r\n\t\t\t\t\t// 微信小程序无法将picker实例传出来，只能通过ref操作\r\n\t\t\t\t\tpicker = this.$refs.uPicker\r\n\t\t\t\t} = e\r\n\t\t\t\tif (columnIndex === 0) {\r\n\t\t\t\t\tthis.$api.service.getCity(this.columnsCity[0][index].id).then(res => {\r\n\t\t\t\t\t\tpicker.setColumnValues(1, res)\r\n\t\t\t\t\t\tthis.columnsCity[1] = res\r\n\t\t\t\t\t\tthis.$api.service.getCity(res[0].id).then(res1 => {\r\n\t\t\t\t\t\t\tpicker.setColumnValues(2, res1)\r\n\t\t\t\t\t\t\tthis.columnsCity[2] = res1\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t})\r\n\t\t\t\t} else if (columnIndex === 1) {\r\n\t\t\t\t\tthis.$api.service.getCity(this.columnsCity[1][index].id).then(res => {\r\n\t\t\t\t\t\tpicker.setColumnValues(2, res)\r\n\t\t\t\t\t\tthis.columnsCity[2] = res\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tconfirmCity(Array) {\r\n\t\t\t\tthis.form.city = Array.value.map((item, index) => {\r\n\t\t\t\t\tif (item == undefined) {\r\n\t\t\t\t\t\treturn this.columnsCity[index][0].title\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\treturn item.title\r\n\t\t\t\t\t}\r\n\t\t\t\t}).join('-')\r\n\t\t\t\tthis.form.city_id = Array.value.map((e, j) => {\r\n\t\t\t\t\tif (e == undefined) {\r\n\t\t\t\t\t\tthis.columnsCity[j][0].id\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\treturn e.id\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\tthis.showCity = false\r\n\t\t\t},\r\n\t\t\tsubmit() { //点击保存\r\n\t\t\tconsole.log(this.form);\r\n\t\t\t\tfor (let key in this.form) {\r\n\t\t\t\t\tif (this.form[key] === '' && !(key == 'lng'||key == 'lat')) {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\t\ttitle: '请填写完整提交'\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\treturn\r\n\r\n\t\t\t\t\t} else if (typeof this.form[key] == 'object' && this.form[key].length == 0) {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\t\ttitle: '请填写完整提交'\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\treturn\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tlet p = /^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$/;\r\n\t\t\t\tif (p.test(this.form.id_code) == false) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\ttitle: '请填写正确的身份证号'\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\tlet phoneReg = /^1[3456789]\\d{9}$/\r\n\t\t\t\tif (!phoneReg.test(this.form.mobile)) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\ttitle: '请填写正确的手机号',\r\n\t\t\t\t\t\tduration: 1000\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\tlet obj = JSON.parse(JSON.stringify(this.form))\r\n\t\t\t\tobj.id_card = [obj.id_card1[0].path, obj.id_card2[0].path]\r\n\t\t\t\tobj.self_img = [obj.self_img[0].path]\r\n\t\t\t\tobj.city_id = obj.city_id.map(item=>{return item.toString()})\r\n\t\t\t\tdelete obj.id_card1\r\n\t\t\t\tdelete obj.id_card2\r\n\t\t\t\tthis.$api.service.saveMasterInfo(obj).then(res => {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ticon: 'success',\r\n\t\t\t\t\t\ttitle: '保存成功'\r\n\t\t\t\t\t})\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\tuni.navigateBack()\r\n\t\t\t\t\t}, 1000)\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\timgUpload(e) {\r\n\t\t\t\tlet {\r\n\t\t\t\t\timagelist,\r\n\t\t\t\t\timgtype\r\n\t\t\t\t} = e;\r\n\t\t\t\tthis.form[imgtype] = imagelist;\r\n\t\t\t},\r\n\t\t\tgetInfo() {\r\n\t\t\t\tthis.$api.service.masterInfo().then(res => {\r\n\t\t\t\t\tlet obj = res\r\n\t\t\t\t\tobj.id_card1 = [{\r\n\t\t\t\t\t\tpath: obj.id_card[0]\r\n\t\t\t\t\t}]\r\n\t\t\t\t\tobj.id_card2 = [{\r\n\t\t\t\t\t\tpath: obj.id_card[1]\r\n\t\t\t\t\t}]\r\n\t\t\t\t\tobj.self_img = [{\r\n\t\t\t\t\t\tpath: obj.self_img[0]\r\n\t\t\t\t\t}]\r\n\t\t\t\t\tfor (let key in this.form) {\r\n\t\t\t\t\t\tthis.form[key] = obj[key]\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthis.form.city_id = this.form.city_id.split(',')\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad() {\r\n\t\t\tthis.getcity(0)\r\n\t\t\tthis.getInfo()\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n\t.page {\r\n\t\tpadding-bottom: 200rpx;\r\n\r\n\t\t.header {\r\n\t\t\twidth: 750rpx;\r\n\t\t\theight: 58rpx;\r\n\t\t\tbackground: #FFF7F1;\r\n\t\t\tline-height: 58rpx;\r\n\t\t\ttext-align: center;\r\n\t\t\tfont-size: 28rpx;\r\n\t\t\tfont-weight: 400;\r\n\t\t}\r\n\r\n\t\t.main {\r\n\t\t\tpadding: 40rpx 30rpx;\r\n\r\n\t\t\t.main_item {\r\n\t\t\t\tmargin-bottom: 20rpx;\r\n\r\n\t\t\t\t.title {\r\n\t\t\t\t\tmargin-bottom: 20rpx;\r\n\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\tfont-weight: 400;\r\n\t\t\t\t\tcolor: #333333;\r\n\r\n\t\t\t\t\tspan {\r\n\t\t\t\t\t\tcolor: #E72427;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\tinput {\r\n\t\t\t\t\twidth: 690rpx;\r\n\t\t\t\t\theight: 110rpx;\r\n\t\t\t\t\tbackground: #F8F8F8;\r\n\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\tfont-weight: 400;\r\n\t\t\t\t\tline-height: 110rpx;\r\n\t\t\t\t\tpadding: 0 40rpx;\r\n\t\t\t\t\tbox-sizing: border-box;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.card {\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tjustify-content: space-between;\r\n\r\n\t\t\t\t\t.card_item {\r\n\t\t\t\t\t\twidth: 332rpx;\r\n\t\t\t\t\t\theight: 332rpx;\r\n\t\t\t\t\t\tbackground: #F2FAFE;\r\n\t\t\t\t\t\tborder-radius: 16rpx 16rpx 16rpx 16rpx;\r\n\t\t\t\t\t\toverflow: hidden;\r\n\r\n\t\t\t\t\t\t.top {\r\n\t\t\t\t\t\t\theight: 266rpx;\r\n\t\t\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\t\t\tpadding-top: 40rpx;\r\n\r\n\t\t\t\t\t\t\t.das {\r\n\t\t\t\t\t\t\t\tmargin: 0 auto;\r\n\t\t\t\t\t\t\t\twidth: 266rpx;\r\n\t\t\t\t\t\t\t\theight: 180rpx;\r\n\t\t\t\t\t\t\t\tborder: 2rpx dashed #2E80FE;\r\n\t\t\t\t\t\t\t\tpadding-top: 28rpx;\r\n\r\n\t\t\t\t\t\t\t\t.up {\r\n\t\t\t\t\t\t\t\t\tmargin: 0 auto;\r\n\t\t\t\t\t\t\t\t\twidth: 210rpx;\r\n\t\t\t\t\t\t\t\t\theight: 130rpx;\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t.bottom {\r\n\t\t\t\t\t\t\theight: 66rpx;\r\n\t\t\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\t\t\tbackground-color: #2E80FE;\r\n\t\t\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\t\t\tfont-weight: 400;\r\n\t\t\t\t\t\t\tcolor: #FFFFFF;\r\n\t\t\t\t\t\t\ttext-align: center;\r\n\t\t\t\t\t\t\tline-height: 66rpx;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.footer {\r\n\t\t\tpadding: 52rpx 30rpx;\r\n\t\t\twidth: 750rpx;\r\n\t\t\tbackground: #FFFFFF;\r\n\t\t\tbox-shadow: 0rpx 0rpx 6rpx 2rpx rgba(193, 193, 193, 0.3);\r\n\t\t\tposition: fixed;\r\n\t\t\tbottom: 0;\r\n\r\n\t\t\t.btn {\r\n\t\t\t\twidth: 690rpx;\r\n\t\t\t\theight: 98rpx;\r\n\t\t\t\tbackground: #2E80FE;\r\n\t\t\t\tborder-radius: 50rpx 50rpx 50rpx 50rpx;\r\n\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\tfont-weight: 500;\r\n\t\t\t\tcolor: #FFFFFF;\r\n\t\t\t\tline-height: 98rpx;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style>", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./master_Info.vue?vue&type=style&index=0&id=68189f56&scoped=true&lang=scss&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./master_Info.vue?vue&type=style&index=0&id=68189f56&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755337031293\n      var cssReload = require(\"E:/BaiduNetdiskDownload/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}