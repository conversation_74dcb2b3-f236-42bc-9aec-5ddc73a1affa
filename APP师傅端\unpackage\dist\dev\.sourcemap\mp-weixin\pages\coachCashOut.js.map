{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/pages/coachCashOut.vue?a891", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/pages/coachCashOut.vue?710c", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/pages/coachCashOut.vue?0b8b", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/pages/coachCashOut.vue?9dcd", "uni-app:///pages/coachCashOut.vue", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/pages/coachCashOut.vue?2c7f", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/pages/coachCashOut.vue?a276"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "Upload", "data", "money", "allmoney", "cardname", "showNameIdModal", "tempForm", "<PERSON><PERSON><PERSON>", "idCode", "id_card1", "id_card2", "methods", "confirmTx", "uni", "title", "icon", "apply_price", "text", "type", "setTimeout", "goCard", "url", "goAll", "change", "getMoney", "getCard", "imgUploadTemp", "console", "imagelist", "e", "imgtype", "saveNameIdInfo", "namePattern", "idPattern", "shi<PERSON><PERSON>", "userId", "payload", "id", "idCard", "res", "onLoad", "onShow", "that"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,qBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqI;AACrI;AACgE;AACL;AACsC;;;AAGjG;AACsN;AACtN,gBAAgB,8NAAU;AAC1B,EAAE,kFAAM;AACR,EAAE,mGAAM;AACR,EAAE,4GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qRAEN;AACP,KAAK;AACL;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,aAAa,2RAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AChDA;AAAA;AAAA;AAAA;AAA21B,CAAgB,22BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;AC6E/2B;AAAA,eACA;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;QAAA;QACAC;QACAC;QACAC;QACAC;MACA;IACA;EACA;EACAC;IACAC;MACA;QACAC;UACAC;UACAC;QACA;QACA;MACA;MACA;QAAAC;QAAAC;QAAAC;MAAA;QACA;QACA;UACAL;YACAE;YACAD;UACA;UACA;UACAK;YACAN;UACA;QACA;MACA;IACA;IACAO;MACAP;QACAQ;MACA;IACA;IACAC;MACA;IACA;IACAC,4BAEA;IACAC;MAAA;MACA;QACA;QACA;MACA;IACA;IACAC;MAAA;MACA;QACA;UACA;QACA;QACA;MACA;IACA;IACA;IACAC;MACAC;MACA,IACAC,YAEAC,EAFAD;QACAE,UACAD,EADAC;MAEA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,kBAMA,iBAJAxB,uCACAC,iCACAC,qCACAC,qCAGA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBACAG;kBACAE;kBACAD;gBACA;gBAAA;cAAA;gBAIA;gBACAkB;gBAAA,IACAA;kBAAA;kBAAA;gBAAA;gBACAnB;kBACAE;kBACAD;gBACA;gBAAA;cAAA;gBAIA;gBACAmB;gBAAA,IACAA;kBAAA;kBAAA;gBAAA;gBACApB;kBACAE;kBACAD;gBACA;gBAAA;cAAA;gBAGAoB;gBACAC;gBACAR;gBACAA;gBACA;gBACAS;kBACA7B;kBACAC;kBACA6B;kBACA;kBACAC;gBACA;gBAAA;gBAAA;gBAAA,OAGA;cAAA;gBAAAC;gBAAA;gBACAZ;gBACA;kBAAA;kBACAd;oBACAE;oBACAD;kBACA;kBACA;kBACA;kBACA;gBACA;kBACAD;oBACAE;oBACAD;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAD;kBACAE;kBACAD;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;EACA;EACA0B;IACA;IACA;EACA;EACAC;IACA;IACA5B;MACA6B;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC/OA;AAAA;AAAA;AAAA;AAAkmD,CAAgB,sjDAAG,EAAC,C;;;;;;;;;;;ACAtnD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/coachCashOut.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/uni-stat/dist/uni-stat.es.js';\nimport Vue from 'vue'\nimport Page from './pages/coachCashOut.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./coachCashOut.vue?vue&type=template&id=79ec8ccc&scoped=true&\"\nvar renderjs\nimport script from \"./coachCashOut.vue?vue&type=script&lang=js&\"\nexport * from \"./coachCashOut.vue?vue&type=script&lang=js&\"\nimport style0 from \"./coachCashOut.vue?vue&type=style&index=0&id=79ec8ccc&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"79ec8ccc\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/coachCashOut.vue\"\nexport default component.exports", "export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./coachCashOut.vue?vue&type=template&id=79ec8ccc&scoped=true&\"", "var components\ntry {\n  components = {\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-icon/u-icon\" */ \"uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    \"u-Input\": function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u--input/u--input\" */ \"uview-ui/components/u--input/u--input.vue\"\n      )\n    },\n    uModal: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-modal/u-modal\" */ \"uview-ui/components/u-modal/u-modal.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.showNameIdModal = false\n    }\n  }\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./coachCashOut.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./coachCashOut.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"page\">\r\n\t\t<view class=\"header\" @tap=\"goCard\">\r\n\t\t\t<view class=\"left\">提现至</view>\r\n\t\t\t<view class=\"right\">{{cardname}}\r\n\t\t\t<u-icon name=\"arrow-right\" color=\"#333\" size=\"16\"></u-icon>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"mid\">\r\n\t\t\t<view class=\"title\">提现金额</view>\r\n\t\t\t<view class=\"top\">\r\n\t\t\t\t<view class=\"t_left\">\r\n\t\t\t\t\t<u--input\r\n\t\t\t\t\t    placeholder=\"请输入提现金额\"\r\n\t\t\t\t\t\ttype=\"number\"\r\n\t\t\t\t\t    border=\"none\"\r\n\t\t\t\t\t    v-model=\"money\"\r\n\t\t\t\t\t    @change=\"change\"\r\n\t\t\t\t\t\t prefixIcon=\"rmb\"\r\n\t\t\t\t\t  ></u--input>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"r_left\" @tap=\"goAll\">全部提现</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"bottom\">\r\n\t\t\t\t可提现金额￥{{allmoney}}\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"btn\" @tap=\"confirmTx\">确认提现</view>\r\n\t\t<text class=\"tips\">温馨提示：提现申请发起后，预计3个工作日内到账。</text>\r\n\t\t\r\n\t\t<u-modal :show=\"showNameIdModal\" title=\"请完善实名信息以确保提现安全\" confirmText=\"保存\" showCancelButton\r\n\t\t\t@confirm=\"saveNameIdInfo\" @cancel=\"showNameIdModal = false\"\r\n\t\t\t:contentStyle=\"{ padding: '40rpx', background: '#ffffff', borderRadius: '16rpx' }\">\r\n\t\t\t<view class=\"slot-content\">\r\n\t\t\t\t<view class=\"main_item\">\r\n\t\t\t\t\t<view class=\"title\"><span style=\"color: #E41F19;\">*</span>姓名</view>\r\n\t\t\t\t\t<input type=\"text\" v-model=\"tempForm.coachName\" placeholder=\"请输入姓名\" class=\"modal-input\" />\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"main_item\">\r\n\t\t\t\t\t<view class=\"title\"><span style=\"color: #E41F19;\">*</span>身份证号</view>\r\n\t\t\t\t\t<input type=\"text\" v-model=\"tempForm.idCode\" placeholder=\"请输入身份证号\" class=\"modal-input\" />\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"main_item\">\r\n\t\t\t\t\t<view class=\"title\"><span style=\"color: #E41F19;\">*</span>上传身份证照片</view>\r\n\t\t\t\t\t<view class=\"card\">\r\n\t\t\t\t\t\t<view class=\"card_item\">\r\n\t\t\t\t\t\t\t<view class=\"top\">\r\n\t\t\t\t\t\t\t\t<view class=\"das\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"up\">\r\n\t\t\t\t\t\t\t\t\t\t<upload @upload=\"imgUploadTemp\" @del=\"imgUploadTemp\"\r\n\t\t\t\t\t\t\t\t\t\t\t:imagelist=\"tempForm.id_card1\" imgtype=\"id_card1\" imgclass=\"id_card_box\"\r\n\t\t\t\t\t\t\t\t\t\t\ttext=\"身份证人像面\" :imgsize=\"1\"></upload>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"bottom\">拍摄人像面</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"card_item\">\r\n\t\t\t\t\t\t\t<view class=\"top\">\r\n\t\t\t\t\t\t\t\t<view class=\"das\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"up\">\r\n\t\t\t\t\t\t\t\t\t\t<upload @upload=\"imgUploadTemp\" @del=\"imgUploadTemp\"\r\n\t\t\t\t\t\t\t\t\t\t\t:imagelist=\"tempForm.id_card2\" imgtype=\"id_card2\" imgclass=\"id_card_box\"\r\n\t\t\t\t\t\t\t\t\t\t\ttext=\"身份证国徽面\" :imgsize=\"1\"></upload>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"bottom\">拍摄国徽面</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</u-modal>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\nimport Upload from '@/components/upload.vue'; // Adjust path to your upload.vue\r\n\texport default {\r\n\t\tcomponents: {\r\n\t\t\tUpload,\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tmoney:'',\r\n\t\t\t\tallmoney:'',\r\n\t\t\t\tcardname:'暂无银行卡',\r\n\t\t\t\tshowNameIdModal: false, // New state for the name/ID modal\r\n\t\t\t\ttempForm: { // Temporary form for name/ID input\r\n\t\t\t\t\tcoachName: '',\r\n\t\t\t\t\tidCode: '',\r\n\t\t\t\t\tid_card1: [],\r\n\t\t\t\t\tid_card2: [],\r\n\t\t\t\t},\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tconfirmTx(){\r\n\t\t\t\tif(this.money*1>this.allmoney*1){\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle:'超过可提现金额',\r\n\t\t\t\t\t\ticon:'none'\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\tthis.$api.technician.applyWallet({apply_price:this.money,text:'',type:2}).then(res=>{\r\n\t\t\t\t\t// console.log(res);\r\n\t\t\t\t\tif(res == 1){\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ticon:'none',\r\n\t\t\t\t\t\t\ttitle:'申请成功'\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\t// uni.$emit('refss')\r\n\t\t\t\t\t\tsetTimeout(()=>{\r\n\t\t\t\t\t\t\tuni.navigateBack()\r\n\t\t\t\t\t\t},1000)\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tgoCard(){\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl:'/pages/bankCard'\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tgoAll(){\r\n\t\t\t\tthis.money = this.allmoney\r\n\t\t\t},\r\n\t\t\tchange(e){\r\n\t\t\t\t\r\n\t\t\t},\r\n\t\t\tgetMoney(){\r\n\t\t\t\tthis.$api.service.masterInfo().then(res=>{\r\n\t\t\t\t\tthis.allmoney = res.service_price\r\n\t\t\t\t\tthis.money = this.allmoney\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tgetCard(){\r\n\t\t\t\tthis.$api.service.getbankcardlist().then(res=>{\r\n\t\t\t\t\tif(res.length > 0){\r\n\t\t\t\t\t\tthis.cardname = res[0].bank_name+\"(\" +res[0].card_no.substr(-4)+\")\"\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// console.log(res);\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// New method for handling image uploads in the tempForm\r\n\t\t\timgUploadTemp(e) {\r\n\t\t\t\tconsole.log('imgUploadTemp event:', e);\r\n\t\t\t\tconst {\r\n\t\t\t\t\timagelist,\r\n\t\t\t\t\timgtype\r\n\t\t\t\t} = e;\r\n\t\t\t\tthis.$set(this.tempForm, imgtype, imagelist);\r\n\t\t\t},\r\n\r\n\t\t\t// New method to save name and ID info\r\n\t\t\tasync saveNameIdInfo() {\r\n\t\t\t\tconst {\r\n\t\t\t\t\tcoachName,\r\n\t\t\t\t\tidCode,\r\n\t\t\t\t\tid_card1,\r\n\t\t\t\t\tid_card2\r\n\t\t\t\t} = this.tempForm;\r\n\r\n\t\t\t\t// 校验必填项\r\n\t\t\t\tif (!coachName || !idCode || id_card1.length === 0 || id_card2.length === 0) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\ttitle: '请填写所有必填项并上传照片'\r\n\t\t\t\t\t});\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// 校验姓名格式：2-20个汉字，可包含·或•\r\n\t\t\t\tconst namePattern = /^[\\u4e00-\\u9fa5·•]{2,20}$/;\r\n\t\t\t\tif (!namePattern.test(coachName)) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\ttitle: '姓名必须为2-20个汉字，可包含·或•'\r\n\t\t\t\t\t});\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// 校验身份证号格式：18位有效格式\r\n\t\t\t\tconst idPattern = /^[1-9]\\d{5}(18|19|20)\\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\\d|3[01])\\d{3}[\\dXx]$/;\r\n\t\t\t\tif (!idPattern.test(idCode)) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\ttitle: '身份证号必须是18位有效格式'\r\n\t\t\t\t\t});\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tlet shifuid = JSON.parse(uni.getStorageSync('shiInfo'))\r\n\t\t\t\tlet userId = (uni.getStorageSync('userId'))\r\n\t\t\t\tconsole.log(shifuid)\r\n\t\t\t\tconsole.log(userId)\r\n\t\t\t\t// Construct the payload for saving name and ID card information\r\n\t\t\t\tconst payload = {\r\n\t\t\t\t\tcoachName: coachName,\r\n\t\t\t\t\tidCode: idCode,\r\n\t\t\t\t\tid: shifuid.id,\r\n\t\t\t\t\t// Corrected payload to join the image paths into a single comma-separated string\r\n\t\t\t\t\tidCard: [id_card1[0].path, id_card2[0].path].join(','),\r\n\t\t\t\t};\r\n\r\n\t\t\t\ttry {\r\n\t\t\t\t\tconst res = await this.$api.shifu.postCrad(payload); // Replace with your actual API call\r\n\t\t\t\t\tconsole.log(res)\r\n\t\t\t\t\tif (res.code === \"200\") { // Assuming \"200\" means success\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ticon: 'success',\r\n\t\t\t\t\t\t\ttitle: '身份信息保存成功',\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\tthis.showNameIdModal = false;\r\n\t\t\t\t\t\t// You might want to re-attempt the quote submission or refresh data here\r\n\t\t\t\t\t\t// For now, let's just close the modal.\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\t\ttitle: res.msg || '身份信息保存失败'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ticon: 'error',\r\n\t\t\t\t\t\ttitle: error.message || '身份信息保存失败'\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad() {\r\n\t\t\tthis.getMoney()\r\n\t\t\tthis.getCard()\r\n\t\t},\r\n\t\tonShow(){\r\n\t\t\tlet that = this\r\n\t\t\tuni.$on('chooseCard',(res)=>{\r\n\t\t\t\tthat.cardname = res.bank_name+\"(\" +res.card_no.substr(-4)+\")\"\r\n\t\t\t})\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.page{\r\n\tbackground-color: #f8f8f8;\r\n\tmin-height: 100vh;\r\n\tpadding: 20rpx 0;\r\n\t.header{\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: space-between;\r\n\t\tfont-size: 28rpx;\r\n\t\tfont-weight: 500;\r\n\t\tcolor: #3B3B3B;\r\n\t\tpadding: 0 30rpx;\r\n\t\twidth: 750rpx;\r\n\t\theight: 118rpx;\r\n\t\tbackground: #FFFFFF;\r\n\t\t.right{\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t}\r\n\t}\r\n\t.mid{\r\n\t\tmargin-top: 20rpx;\r\n\t\twidth: 750rpx;\r\n\t\theight: 276rpx;\r\n\t\tbackground: #FFFFFF;\r\n\t\tpadding: 0 30rpx;\r\n\t\tpadding-top: 40rpx;\r\n\t\t.title{\r\n\t\t\tfont-size: 28rpx;\r\n\t\t\tfont-weight: 500;\r\n\t\t\tcolor: #3B3B3B;\r\n\t\t}\r\n\t\t.top{\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: flex-end;\r\n\t\t\tjustify-content: space-between;\r\n\t\t\tpadding-top: 28rpx;\r\n\t\t\tpadding-bottom: 20rpx;\r\n\t\t\tborder-bottom: 2rpx solid #F2F3F6;\r\n\t\t\t.r_left{\r\n\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\tfont-weight: 500;\r\n\t\t\t\tcolor: #E51837;\r\n\t\t\t}\r\n\t\t}\r\n\t\t.bottom{\r\n\t\t\tpadding-top: 20rpx;\r\n\t\t\tfont-size: 24rpx;\r\n\t\t\tfont-weight: 500;\r\n\t\t\tcolor: #999999;\r\n\t\t\t\r\n\t\t}\r\n\t}\r\n\t.btn{\r\n\t\tmargin: 0 auto;\r\n\t\tmargin-top: 60rpx;\r\n\t\twidth: 690rpx;\r\n\t\theight: 98rpx;\r\n\t\tbackground: #2E80FE;\r\n\t\tborder-radius: 50rpx 50rpx 50rpx 50rpx;\r\n\t\tfont-size: 32rpx;\r\n\t\tfont-weight: 500;\r\n\t\tcolor: #FFFFFF;\r\n\t\tline-height:98rpx;\r\n\t\ttext-align: center;\r\n\t}\r\n\t.tips{\r\n\t\tdisplay: block;\r\n\t\tfont-size: 24rpx;\r\n\t\tfont-weight: 500;\r\n\t\tcolor: #999999;\r\n\t\ttext-align: center;\r\n\t\tmargin-top: 20rpx;\r\n\t}\r\n}\r\n\r\n/* Optimized styles for the name/ID modal content */\r\n.slot-content {\r\n\tpadding: 20rpx 0;\r\n}\r\n\r\n.main_item {\r\n\tmargin-bottom: 32rpx;\r\n\tpadding: 0 24rpx;\r\n\r\n\t.title {\r\n\t\tmargin-bottom: 16rpx;\r\n\t\tfont-size: 30rpx;\r\n\t\tfont-weight: 500;\r\n\t\tcolor: #1a1a1a;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\r\n\t\tspan {\r\n\t\t\tcolor: #E41F19;\r\n\t\t\tmargin-right: 8rpx;\r\n\t\t}\r\n\t}\r\n\r\n\t.modal-input {\r\n\t\twidth: 100%;\r\n\t\theight: 80rpx;\r\n\t\tbackground: #f8f8f8;\r\n\t\tborder: 1rpx solid #e5e7eb;\r\n\t\tborder-radius: 12rpx;\r\n\t\tfont-size: 28rpx;\r\n\t\tfont-weight: 400;\r\n\t\tline-height: 80rpx;\r\n\t\tpadding: 0 24rpx;\r\n\t\tbox-sizing: border-box;\r\n\t\ttransition: all 0.2s ease-in-out;\r\n\r\n\t\t&:focus {\r\n\t\t\tborder-color: #2e80fe;\r\n\t\t\tbackground: #ffffff;\r\n\t\t\tbox-shadow: 0 0 8rpx rgba(46, 128, 254, 0.2);\r\n\t\t}\r\n\r\n\t\t&:disabled {\r\n\t\t\tbackground: #f0f0f0;\r\n\t\t\tcolor: #999;\r\n\t\t\tcursor: not-allowed;\r\n\t\t}\r\n\t}\r\n\r\n\t.card {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\tgap: 16rpx;\r\n\r\n\t\t.card_item {\r\n\t\t\twidth: 48%;\r\n\t\t\tbackground: #f2fafe;\r\n\t\t\tborder-radius: 16rpx;\r\n\t\t\toverflow: hidden;\r\n\t\t\tbox-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);\r\n\t\t\ttransition: transform 0.2s ease-in-out;\r\n\r\n\t\t\t&:hover {\r\n\t\t\t\ttransform: translateY(-4rpx);\r\n\t\t\t}\r\n\r\n\t\t\t.top {\r\n\t\t\t\theight: 180rpx;\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\tpadding-top: 20rpx;\r\n\r\n\t\t\t\t.das {\r\n\t\t\t\t\tmargin: 0 auto;\r\n\t\t\t\t\twidth: 85%;\r\n\t\t\t\t\theight: 120rpx;\r\n\t\t\t\t\tborder: 2rpx dashed #2e80fe;\r\n\t\t\t\t\tborder-radius: 8rpx;\r\n\t\t\t\t\tpadding: 10rpx;\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tjustify-content: center;\r\n\r\n\t\t\t\t\t.up {\r\n\t\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\t\theight: 100%;\r\n\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t\tjustify-content: center;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.bottom {\r\n\t\t\t\theight: 60rpx;\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\tbackground: linear-gradient(90deg, #2e80fe 0%, #5ba0ff 100%);\r\n\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\tfont-weight: 500;\r\n\t\t\t\tcolor: #ffffff;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\tline-height: 60rpx;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n</style>\r\n", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./coachCashOut.vue?vue&type=style&index=0&id=79ec8ccc&scoped=true&lang=scss&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./coachCashOut.vue?vue&type=style&index=0&id=79ec8ccc&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755334684369\n      var cssReload = require(\"E:/BaiduNetdiskDownload/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}