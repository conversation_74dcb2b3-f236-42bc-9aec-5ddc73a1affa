{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/user/order_details.vue?14a5", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/user/order_details.vue?17be", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/user/order_details.vue?0da2", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/user/order_details.vue?087c", "uni-app:///user/order_details.vue", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/user/order_details.vue?6397", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/user/order_details.vue?59eb"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "show", "afterSalesShow", "afterSalesValue", "info", "list", "title", "desc", "infoList", "children", "id", "methods", "viewWarranty", "uni", "icon", "openPdfInWebview", "url", "opengenerate", "console", "confirm", "setTimeout", "cancelOrder", "openAfterSales", "submitAfterSales", "orderId", "remark", "call", "phoneNumber", "getInfo", "name", "value", "isBasicInfo", "onUnload", "delta", "onLoad"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,sBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsI;AACtI;AACiE;AACL;AACsC;;;AAGlG;AACsN;AACtN,gBAAgB,8NAAU;AAC1B,EAAE,mFAAM;AACR,EAAE,oGAAM;AACR,EAAE,6GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,2RAEN;AACP,KAAK;AACL;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,aAAa,2RAEN;AACP,KAAK;AACL;AACA,aAAa,yTAEN;AACP,KAAK;AACL;AACA,aAAa,qRAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtFA;AAAA;AAAA;AAAA;AAA41B,CAAgB,42BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC0Fh3B;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;QACAC;QACAC;MACA;MACAC;QACAF;QACAG;MACA,GACA;QACAH;QACAG;MACA,GACA;QACAH;QACAG;MACA,GACA;QACAH;QACAG;MACA,GACA;QACAH;QACAG;MACA,EACA;MACAC;IACA;EACA;EACAC;IAGAC;MACA;QACA;;QAUA;QACA;MAEA;QACAC;UACAC;UACAR;QACA;MACA;IACA;IAEA;IACAS;MACA;;MAGA;MACA;;MAEA;MACAF;QACAG;MACA;IACA;IAEAC;MAEA;QACAP;MAEA;QACAQ;MACA;IACA;IAqBAC;MACA;QAAAT;MAAA;QACAG;UACAC;UACAR;QACA;QACAO;QACAO;UACAP;QACA;MACA;IACA;IACAQ;MACA;IACA;IACAC;MACA;IACA;IACAC;MAAA;MACA;QACAV;UACAC;UACAR;QACA;QACA;MACA;MACA;MACA;QACAkB;QACAC;MACA;QACAP;QACA;UACAL;YACAC;YACAR;UACA;QACA;UACAO;YACAC;YACAR;UACA;UACA;UACA;QACA;MAEA;QACAO;UACAC;UACAR;QACA;MACA;IACA;IACAoB;MACA;QACAb;UACAP;UACAQ;QACA;QACA;MACA;MACAD;QACAc;MACA;IACA;IACAC;MAAA;MACA;QACAV;QACA;QACA;UACA;YACAZ;YACAC;UACA;QACA;QACA;UACA;YACAD;YACAC;UACA;QACA;UACA;YACAD;YACAC;UACA;QACA;QACA;UACAsB;UACAC;QACA,GACA;UACAD;UACAC;QACA;QACA;QACA;QACA;QACA;QAAA,CACA;QACA;QACA;QACA,+BACA;UACAD;UACAC;UACAC;QACA,GACA;UACAF;UACAC;UACAC;QACA,EACA;QACA;UACAF;UACAC;QAEA,GACA;UACAD;UACAC;QAEA,EAEA;QACA;UACAD;UACAC;QAEA,GACA;UACAD;UACAC;QAEA;QACA;UACAD;UACAC;QACA,GACA;UACAD;UACAC;QACA,EACA;MACA;IACA;EACA;EACAE;IACA;IACA;IACA;MACAnB;QACAoB;MACA;IACA;EACA;EACAC;IACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AClWA;AAAA;AAAA;AAAA;AAAmmD,CAAgB,ujDAAG,EAAC,C;;;;;;;;;;;ACAvnD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "user/order_details.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/uni-stat/dist/uni-stat.es.js';\nimport Vue from 'vue'\nimport Page from './user/order_details.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./order_details.vue?vue&type=template&id=f98852ca&scoped=true&\"\nvar renderjs\nimport script from \"./order_details.vue?vue&type=script&lang=js&\"\nexport * from \"./order_details.vue?vue&type=script&lang=js&\"\nimport style0 from \"./order_details.vue?vue&type=style&index=0&id=f98852ca&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"f98852ca\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"user/order_details.vue\"\nexport default component.exports", "export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./order_details.vue?vue&type=template&id=f98852ca&scoped=true&\"", "var components\ntry {\n  components = {\n    uModal: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-modal/u-modal\" */ \"uview-ui/components/u-modal/u-modal.vue\"\n      )\n    },\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-icons/components/uni-icons/uni-icons\" */ \"@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue\"\n      )\n    },\n    uSteps: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-steps/u-steps\" */ \"uview-ui/components/u-steps/u-steps.vue\"\n      )\n    },\n    uStepsItem: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-steps-item/u-steps-item\" */ \"uview-ui/components/u-steps-item/u-steps-item.vue\"\n      )\n    },\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-icon/u-icon\" */ \"uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.info.coachInfo\n    ? [-1, 1, -2, -3].includes(_vm.info.payType)\n    : null\n  var l1 = _vm.__map(_vm.infoList, function (item, index) {\n    var $orig = _vm.__get_orig(item)\n    var l0 =\n      item.title === \"服务信息\"\n        ? item.children.filter(function (child) {\n            return child.isBasicInfo\n          })\n        : null\n    return {\n      $orig: $orig,\n      l0: l0,\n    }\n  })\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.show = false\n    }\n    _vm.e1 = function ($event) {\n      _vm.afterSalesShow = false\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        l1: l1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./order_details.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./order_details.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"page\">\n\t\t<u-modal :show=\"show\" title=\"取消订单\" content='确认取消该订单吗' showCancelButton @cancel=\"show= false\" @confirm=\"confirm\"></u-modal>\n\t\t<u-modal :show=\"afterSalesShow\" title=\"申请售后\" showCancelButton @cancel=\"afterSalesShow= false\" @confirm=\"submitAfterSales\">\n\t\t\t<view class=\"after-sales-input\">\n\t\t\t\t<view class=\"\">\n\t\t\t\t\t<textarea v-model=\"afterSalesValue\" placeholder=\"请输入售后内容\" style=\" padding: 20rpx; border: 2rpx solid #E9E9E9; border-radius: 8rpx; writing-mode: horizontal-tb; text-align: left;\"></textarea>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</u-modal>\n\t\t<view  class=\"header\" v-if=\"info.coachInfo\">\n\t\t\t<view class=\"top\" >\n\t\t\t\t<view class=\"left\">\n\t\t\t\t\t<view class=\"\" style=\"display: flex;align-items: center;\">\n\t\t\t\t\t\t<view class=\"name\">{{info.coachInfo.coachName}}</view>\n\t\t\t\t\t\t<view class=\"\" style=\"background-color: #fac21f;color: #fff;width: fit-content;padding: 5rpx 10rpx;font-size: 24rpx;margin-left: 20rpx;border-radius: 6rpx;\" v-if=\"info.coachInfo.label_name\">{{info.coachInfo.label_name}}</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"time\">{{info.createTime}}</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"right\">\n\t\t\t\t\t<image :src=\"info.coachInfo.selfImg\" mode=\"\"></image>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view v-if=\"![-1, 1, -2, -3].includes(info.payType)\" class=\"bott\" @click=\"call\">\n\t\t\t\t<view class=\"box\"><uni-icons type=\"phone-filled\" size=\"16\" color=\"#fff\"></uni-icons></view>\n\t\t\t\t<text>打电话给师傅</text>\n\t\t\t</view>\n\t\t</view>\n\t\t<view v-if=\"info.payType===7\"  class=\"after-sales-btn\" @click=\"openAfterSales\">\n\t\t\t去售后\n\t\t</view>\n\t\t<view v-if=\"info.payType===7 && info.warrantyType === 1\"  class=\"after-sales-btn\" @click=\"viewWarranty\">\n\t\t\t查看质保单\n\t\t</view>\n\t\t<view v-else-if=\"info.payType===7\"  class=\"after-sales-btn\" @click=\"opengenerate\">\n\t\t\t生成质保单\n\t\t</view>\n\t\t<view class=\"schedule\">\n\t\t\t<u-steps current=\"4\" direction=\"column\">\n\t\t\t\t<u-steps-item :title=\"item.title\" :desc=\"item.desc\" v-for=\"(item,index) in list\" :key=\"index\">\n\t\t\t\t\t<view class=\"slot-icon\" slot=\"icon\">\n\t\t\t\t\t\t<view class=\"\" style=\"border-radius: 50%;background-color:#00b26a;padding: 5rpx;\">\n\t\t\t\t\t\t\t<u-icon name=\"checkbox-mark\" color=\"#ffffff\" size=\"14\"></u-icon>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</u-steps-item>\n\t\t\t</u-steps>\n\t\t</view>\n\t\t<view class=\"info\" v-for=\"(item,index) in infoList\" :key=\"index\">\n\t\t\t<view class=\"title\">{{item.title}}</view>\n\t\t\t<!-- 服务信息特殊处理，显示商品卡片布局 -->\n\t\t\t<view v-if=\"item.title === '服务信息'\" class=\"service-info\">\n\t\t\t\t<!-- 服务类型和师傅信息 -->\n\t\t\t\t<view class=\"info_item\" v-for=\"(newItem,newIndex) in item.children.filter(child => child.isBasicInfo)\" :key=\"newIndex\">\n\t\t\t\t\t<view class=\"left\">{{newItem.name}}</view>\n\t\t\t\t\t<view class=\"right\">{{newItem.value}}</view>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- 商品卡片列表 -->\n\t\t\t\t<view class=\"goods-list\">\n\t\t\t\t\t<view class=\"goods-card\" v-for=\"(goods, goodsIndex) in info.orderGoods\" :key=\"goodsIndex\">\n\t\t\t\t\t\t<view class=\"goods-main\">\n\t\t\t\t\t\t\t<image class=\"goods-image\" :src=\"goods.goodsCover\" mode=\"aspectFill\"></image>\n\t\t\t\t\t\t\t<view class=\"goods-content\">\n\t\t\t\t\t\t\t\t<view class=\"goods-name\">{{goods.goodsName}}</view>\n\t\t\t\t\t\t\t\t<view class=\"goods-details\">\n\t\t\t\t\t\t\t\t\t<view class=\"detail-item\" v-for=\"setting in goods.priceSetting\" :key=\"setting.id\">\n\t\t\t\t\t\t\t\t\t\t<text class=\"detail-text\" v-if=\"setting.val && setting.val !== '' && setting.inputType !== 2\">{{setting.problemDesc}}：{{setting.val}}</text>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"goods-right\">\n\t\t\t\t\t\t\t\t<view class=\"goods-price\" v-if=\"goods.price > 0\">¥{{goods.price}}</view>\n\t\t\t\t\t\t\t\t<view class=\"goods-num\">x{{goods.num}}</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<!-- 其他信息正常显示 -->\n\t\t\t<view v-else class=\"info_item\" v-for=\"(newItem,newIndex) in item.children\" :key=\"newIndex\">\n\t\t\t\t<view class=\"left\">{{newItem.name}}</view>\n\t\t\t\t<view class=\"right\">{{newItem.value}}</view>\n\t\t\t</view>\n\t\t</view>\n\t\t<view class=\"btn\" v-if=\"info.payType<=1 && info.payType != -1\" @click=\"cancelOrder\">取消订单</view>\n\t</view>\n</template>\n\n<script>\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tshow: false,\n\t\t\t\tafterSalesShow: false,\n\t\t\t\tafterSalesValue: '',\n\t\t\t\tinfo: {},\n\t\t\t\tlist: [{\n\t\t\t\t\ttitle: '订单已生成',\n\t\t\t\t\tdesc: '预定成功，将尽快为主人派单'\n\t\t\t\t}],\n\t\t\t\tinfoList: [{\n\t\t\t\t\t\ttitle: '预约信息',\n\t\t\t\t\t\tchildren: []\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\ttitle: \"服务信息\",\n\t\t\t\t\t\tchildren: []\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\ttitle: \"费用明细\",\n\t\t\t\t\t\tchildren: []\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\ttitle: \"优惠明细\",\n\t\t\t\t\t\tchildren: []\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\ttitle: \"下单明细\",\n\t\t\t\t\t\tchildren: []\n\t\t\t\t\t}\n\t\t\t\t],\n\t\t\t\tid: ''\n\t\t\t}\n\t\t},\n\t\tmethods: {\n\t\t\t\n\t\t\t\n\t\t\tviewWarranty() {\n\t\t\t\tif (this.info.orderWarrantyImg) {\n\t\t\t\t\t// 直接在浏览器中查看质保单\n\t\t\t\t\t// #ifdef H5\n\t\t\t\t\twindow.open(this.info.orderWarrantyImg, '_blank');\n\t\t\t\t\t// #endif\n\n\t\t\t\t\t// #ifdef APP-PLUS\n\t\t\t\t\tplus.runtime.openURL(this.info.orderWarrantyImg);\n\t\t\t\t\t// #endif\n\n\t\t\t\t\t// #ifdef MP\n\t\t\t\t\t// 小程序环境下直接使用webview预览\n\t\t\t\t\tthis.openPdfInWebview();\n\t\t\t\t\t// #endif\n\t\t\t\t} else {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\ttitle: '质保单不存在'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 使用webview预览PDF的方法\n\t\t\topenPdfInWebview() {\n\t\t\t\t// 构建PDF预览URL，支持多种在线预览服务\n\t\t\t\t\n\n\t\t\t\t// 方案2：如果上面的服务不可用，可以尝试其他服务\n\t\t\t\t// previewUrl = `https://mozilla.github.io/pdf.js/web/viewer.html?file=${encodeURIComponent(this.info.orderWarrantyImg)}`;\n\n\t\t\t\t// 跳转到webview页面预览PDF\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: `/user/webview?url=${encodeURIComponent(this.info.orderWarrantyImg)}&title=${encodeURIComponent('质保单')}`\n\t\t\t\t});\n\t\t\t},\n\n\t\t\topengenerate(){\n\n\t\t\t\tthis.$api.service.getGenerate({\n\t\t\t\t\tid:this.id\n\n\t\t\t\t}).then(res=>{\n\t\t\t\t\tconsole.log(res)\n\t\t\t\t})\n\t\t\t},\n\t\t\t\n\t\t\t\n\t\t\t\n\t\t\t\n\t\t\t\n\t\t\t\n\t\t\t\n\t\t\t\n\t\t\t\n\t\t\t\n\t\t\t\n\t\t\t\n\t\t\t\n\t\t\t\n\t\t\t\n\t\t\t\n\t\t\t\n\t\t\t\n\t\t\t\n\t\t\t\n\t\t\tconfirm() {\n\t\t\t\tthis.$api.service.cancelOrder({id: this.info.id}).then(res => {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\ttitle: '取消成功'\n\t\t\t\t\t})\n\t\t\t\t\tuni.$emit('cancelOr')\n\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\tuni.navigateBack()\n\t\t\t\t\t}, 1000)\n\t\t\t\t})\n\t\t\t},\n\t\t\tcancelOrder() {\n\t\t\t\tthis.show = true\n\t\t\t},\n\t\t\topenAfterSales() {\n\t\t\t\tthis.afterSalesShow = true\n\t\t\t},\n\t\t\tsubmitAfterSales() {\n\t\t\t\tif (!this.afterSalesValue.trim()) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\ttitle: '请输入售后内容'\n\t\t\t\t\t})\n\t\t\t\t\treturn\n\t\t\t\t}\n\t\t\t\t// Here you would typically call an API to submit the after-sales request\n\t\t\t\tthis.$api.service.submitAfterSales({\n\t\t\t\t\torderId: this.id,\n\t\t\t\t\tremark: this.afterSalesValue\n\t\t\t\t}).then(res => {\n\t\t\t\t\tconsole.log(res)\n\t\t\t\t\tif(res.code===\"-1\"){\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\t\ttitle: res.msg\n\t\t\t\t\t\t})\n\t\t\t\t\t}else{\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\t\ttitle: '售后申请提交成功'\n\t\t\t\t\t\t})\n\t\t\t\t\t\tthis.afterSalesShow = false\n\t\t\t\t\t\tthis.afterSalesValue = ''\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t}).catch(err => {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\ttitle: '提交失败，请重试'\n\t\t\t\t\t})\n\t\t\t\t})\n\t\t\t},\n\t\t\tcall() {\n\t\t\t\tif (!this.info.coachInfo.mobile || this.info.coachInfo.mobile.includes('*')) {\n\t\t\t\t    uni.showToast({\n\t\t\t\t      title: '无法拨打电话，号码不可用',\n\t\t\t\t      icon: 'none'\n\t\t\t\t    });\n\t\t\t\t    return;\n\t\t\t\t  }\n\t\t\t\tuni.makePhoneCall({\n\t\t\t\t\tphoneNumber: this.info.coachInfo.mobile\n\t\t\t\t});\n\t\t\t},\n\t\t\tgetInfo() {\n\t\t\t\tthis.$api.service.orderdet(this.id).then(res => {\n\t\t\t\t\tconsole.log(res)\n\t\t\t\t\tthis.info = res.data\n\t\t\t\t\tif (this.info.coachInfo) {\n\t\t\t\t\t\tthis.list.push({\n\t\t\t\t\t\t\ttitle: '订单已派单',\n\t\t\t\t\t\t\tdesc: `订单交给${this.info.coachInfo.coachName}，将督促师傅尽快跟您联系`\n\t\t\t\t\t\t})\n\t\t\t\t\t}\n\t\t\t\t\tif (this.info.payType == 7) {\n\t\t\t\t\t\tthis.list.push({\n\t\t\t\t\t\t\ttitle: '订单完成',\n\t\t\t\t\t\t\tdesc: `订单已完成`\n\t\t\t\t\t\t})\n\t\t\t\t\t} else if (this.info.payType == -1) {\n\t\t\t\t\t\tthis.list.push({\n\t\t\t\t\t\t\ttitle: '订单取消',\n\t\t\t\t\t\t\tdesc: `订单已取消`\n\t\t\t\t\t\t})\n\t\t\t\t\t}\n\t\t\t\t\tthis.infoList[0].children = [{\n\t\t\t\t\t\t\tname: '预约时间',\n\t\t\t\t\t\t\tvalue: this.info.startTime\n\t\t\t\t\t\t},\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tname: '服务地址',\n\t\t\t\t\t\t\tvalue: this.info.addressInfo.address + this.info.addressInfo.addressInfo + this.info.addressInfo.houseNumber\n\t\t\t\t\t\t},\n\t\t\t\t\t\t// {\n\t\t\t\t\t\t// \tname: '预约服务',\n\t\t\t\t\t\t// \tvalue: this.info.orderGoods[0].goodsName\n\t\t\t\t\t\t// }\n\t\t\t\t\t]\n\t\t\t\t\t// 构建服务信息，只包含基本信息（服务类型、师傅信息）\n\t\t\t\t\t// 商品详情将在模板中直接渲染\n\t\t\t\t\tthis.infoList[1].children = [\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tname: '服务类型',\n\t\t\t\t\t\t\tvalue: this.info.type == 0 ? '一口价' : '报价',\n\t\t\t\t\t\t\tisBasicInfo: true\n\t\t\t\t\t\t},\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tname: '服务师傅',\n\t\t\t\t\t\t\tvalue: this.info.coachInfo ? this.info.coachInfo.coachName : '',\n\t\t\t\t\t\t\tisBasicInfo: true\n\t\t\t\t\t\t}\n\t\t\t\t\t];\n\t\t\t\t\tthis.infoList[2].children = [{\n\t\t\t\t\t\tname: '服务费用',\n\t\t\t\t\t\tvalue: this.info.payPrice + '元',\r\n\t\t\t\t\t\t\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tname: '',\r\n\t\t\t\t\t\tvalue: '',\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t]\n\t\t\t\t\tthis.infoList[3].children = [{\n\t\t\t\t\t\tname: '优惠券',\n\t\t\t\t\t\tvalue: this.info.couponInfo ? '-' + this.info.couponInfo.discount + '元' : '无',\n\t\t\t\t\t\t\t\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tname: '',\r\n\t\t\t\t\t\tvalue: '',\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t}]\n\t\t\t\t\tthis.infoList[4].children = [{\n\t\t\t\t\t\t\tname: '订单号码',\n\t\t\t\t\t\t\tvalue: this.info.orderCode\n\t\t\t\t\t\t},\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tname: '下单时间',\n\t\t\t\t\t\t\tvalue: this.info.createTime \n\t\t\t\t\t\t}\n\t\t\t\t\t]\n\t\t\t\t})\n\t\t\t}\n\t\t},\n\t\tonUnload() {\n\t\t\tlet pageArr = getCurrentPages()\n\t\t\tlet length = pageArr.length\n\t\t\tif (pageArr[length - 2].route == \"/pages/order_success\") {\n\t\t\t\tuni.navigateBack({\n\t\t\t\t\tdelta: 9\n\t\t\t\t})\n\t\t\t}\n\t\t},\n\t\tonLoad(options) {\n\t\t\tthis.id = options.id\n\t\t\tthis.getInfo()\n\t\t}\n\t}\n</script>\n\n<style scoped lang=\"scss\">\n\t.page {\n\t\tbackground-color: #f8f8f8;\n\t\theight: 100vh;\n\t\toverflow: auto;\n\t\tpadding: 40rpx 30rpx;\n\n\t\t.header {\n\t\t\twidth: 690rpx;\n\t\t\theight: 274rpx;\n\t\t\tbackground: #FFFFFF;\n\t\t\tborder-radius: 24rpx 24rpx 24rpx 24rpx;\n\t\t\tpadding: 28rpx 36rpx;\n\n\t\t\t.top {\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\t\t\t\tjustify-content: space-between;\n\t\t\t\tpadding-bottom: 42rpx;\n\t\t\t\tborder-bottom: 2rpx solid #E9E9E9;\n\n\t\t\t\t.left {\n\t\t\t\t\t.name {\n\t\t\t\t\t\tfont-size: 32rpx;\n\t\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\t\tcolor: #333333;\n\t\t\t\t\t}\n\n\t\t\t\t\t.time {\n\t\t\t\t\t\tmargin-top: 20rpx;\n\t\t\t\t\t\tfont-size: 24rpx;\n\t\t\t\t\t\tfont-weight: 400;\n\t\t\t\t\t\tcolor: #999999;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t.right {\n\t\t\t\t\timage {\n\t\t\t\t\t\twidth: 80rpx;\n\t\t\t\t\t\theight: 80rpx;\n\t\t\t\t\t\tborder-radius: 50%;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.bott {\n\t\t\t\theight: 100rpx;\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\n\t\t\t\ttext {\n\t\t\t\t\tmargin-left: 20rpx;\n\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\tcolor: #2E80FE;\n\t\t\t\t}\n\n\t\t\t\t.box {\n\t\t\t\t\twidth: 42rpx;\n\t\t\t\t\theight: 42rpx;\n\t\t\t\t\tbackground: #2E80FE;\n\t\t\t\t\tborder-radius: 50%;\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\talign-items: center;\n\t\t\t\t\tjustify-content: center;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t.after-sales-btn {\n\t\t\twidth: 686rpx;\n\t\t\theight: 88rpx;\n\t\t\tfont-weight: 700;\n\t\t\tbackground: #FFFFFF;\n\t\t\tborder-radius: 20rpx;\n\t\t\tfont-size: 28rpx;\n\t\t\tfont-weight: 400;\n\t\t\tcolor:#2E80FE;\n\t\t\tline-height: 88rpx;\n\t\t\ttext-align: center;\n\t\t\tmargin: 16rpx auto;\n\t\t}\n\n\t\t.after-sales-input {\n\t\t\tpadding: 20rpx;\n\t\t}\n\n\t\t.schedule {\n\t\t\twidth: 690rpx;\n\t\t\tpadding: 32rpx 36rpx;\n\t\t\tbackground: #FFFFFF;\n\t\t\tborder-radius: 24rpx 24rpx 24rpx 24rpx;\n\n\t\t\t.slot-icon {\n\t\t\t\timage {\n\t\t\t\t\twidth: 32rpx;\n\t\t\t\t\theight: 32rpx;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t::v-deep .u-steps-item__line {\n\t\t\t\tbackground-color: #000 !important;\n\t\t\t}\n\t\t}\n\n\t\t.info {\n\t\t\tmargin-top: 20rpx;\n\t\t\twidth: 690rpx;\n\t\t\tbackground: #FFFFFF;\n\t\t\tborder-radius: 24rpx 24rpx 24rpx 24rpx;\n\t\t\tpadding: 28rpx 36rpx;\n\n\t\t\t.title {\n\t\t\t\tfont-size: 32rpx;\n\t\t\t\tfont-weight: 500;\n\t\t\t\tcolor: #333333;\n\t\t\t\tmargin-bottom: 26rpx;\n\t\t\t}\n\n\t\t\t.info_item {\n\t\t\t\tdisplay: flex;\n\t\t\t\tpadding: 24rpx 0;\n\t\t\t\talign-items: center;\n\t\t\t\tjustify-content: space-between;\n\t\t\t\tborder-top: 2rpx solid #E9E9E9;\n\n\t\t\t\t.left {\n\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\tfont-weight: 400;\n\t\t\t\t\tcolor: #ADADAD;\n\t\t\t\t}\n\n\t\t\t\t.right {\n\t\t\t\t\tmax-width: 500rpx;\n\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\tfont-weight: 400;\n\t\t\t\t\tcolor: #333333;\n\t\t\t\t\ttext-align: right;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.service-info {\n\t\t\t\t.goods-list {\n\t\t\t\t\tmargin-top: 20rpx;\n\t\t\t\t}\n\n\t\t\t\t.goods-card {\n\t\t\t\t\tmargin-bottom: 20rpx;\n\t\t\t\t\tpadding: 20rpx 0;\n\t\t\t\t\tborder-top: 2rpx solid #E9E9E9;\n\n\t\t\t\t\t&:last-child {\n\t\t\t\t\t\tmargin-bottom: 0;\n\t\t\t\t\t}\n\n\t\t\t\t\t.goods-main {\n\t\t\t\t\t\tdisplay: flex;\n\t\t\t\t\t\talign-items: flex-start;\n\n\t\t\t\t\t\t.goods-image {\n\t\t\t\t\t\t\twidth: 120rpx;\n\t\t\t\t\t\t\theight: 120rpx;\n\t\t\t\t\t\t\tborder-radius: 12rpx;\n\t\t\t\t\t\t\tflex-shrink: 0;\n\t\t\t\t\t\t\tmargin-right: 20rpx;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t.goods-content {\n\t\t\t\t\t\t\tflex: 1;\n\t\t\t\t\t\t\tmin-width: 0;\n\n\t\t\t\t\t\t\t.goods-name {\n\t\t\t\t\t\t\t\tfont-size: 32rpx;\n\t\t\t\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\t\t\t\tcolor: #333333;\n\t\t\t\t\t\t\t\tmargin-bottom: 12rpx;\n\t\t\t\t\t\t\t\tline-height: 1.4;\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t.goods-details {\n\t\t\t\t\t\t\t\t.detail-item {\n\t\t\t\t\t\t\t\t\tmargin-bottom: 8rpx;\n\n\t\t\t\t\t\t\t\t\t.detail-text {\n\t\t\t\t\t\t\t\t\t\tfont-size: 24rpx;\n\t\t\t\t\t\t\t\t\t\tcolor: #666666;\n\t\t\t\t\t\t\t\t\t\tline-height: 1.3;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t.goods-right {\n\t\t\t\t\t\t\tflex-shrink: 0;\n\t\t\t\t\t\t\ttext-align: right;\n\t\t\t\t\t\t\tdisplay: flex;\n\t\t\t\t\t\t\tflex-direction: column;\n\t\t\t\t\t\t\talign-items: flex-end;\n\t\t\t\t\t\t\tjustify-content: space-between;\n\t\t\t\t\t\t\theight: 120rpx;\n\n\t\t\t\t\t\t\t.goods-price {\n\t\t\t\t\t\t\t\tfont-size: 32rpx;\n\t\t\t\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\t\t\t\tcolor: #FF4444;\n\t\t\t\t\t\t\t\tmargin-bottom: 8rpx;\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t.goods-num {\n\t\t\t\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\t\t\t\tcolor: #999999;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t.btn {\n\t\t\tmargin: 0 auto;\n\t\t\tmargin-top: 40rpx;\n\t\t\twidth: 686rpx;\n\t\t\theight: 88rpx;\n\t\t\tbackground: #2E80FE;\n\t\t\tborder-radius: 44rpx 44rpx 44rpx 44rpx;\n\t\t\tfont-size: 28rpx;\n\t\t\tfont-weight: 400;\n\t\t\tcolor: #FFFFFF;\n\t\t\tline-height: 88rpx;\n\t\t\ttext-align: center;\n\t\t}\n\t}\n</style>", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./order_details.vue?vue&type=style&index=0&id=f98852ca&scoped=true&lang=scss&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./order_details.vue?vue&type=style&index=0&id=f98852ca&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755337031996\n      var cssReload = require(\"E:/BaiduNetdiskDownload/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}